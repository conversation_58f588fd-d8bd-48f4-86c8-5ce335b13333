
import React, { forwardRef } from "react";
import { Employee, Withdraw } from "@/types";
import { Printer } from "lucide-react";

type ReceiptProps = {
  employee: Employee;
  withdraw: Withdraw;
  // يمكن تمرير ref لإتاحة الطباعة
};

const EmployeeWithdrawReceipt = forwardRef<HTMLDivElement, ReceiptProps>(
  ({ employee, withdraw }, ref) => (
    <div
      ref={ref}
      className="w-[350px] mx-auto p-4 border rounded shadow bg-white text-gray-900"
      dir="rtl"
    >
      <div className="flex items-center gap-2 mb-3">
        <Printer className="w-4 h-4" />
        <span className="font-bold text-lg">وصل سحب للموظف</span>
      </div>
      <hr className="mb-2" />
      <div className="flex flex-col gap-1 mb-3">
        <span>
          <span className="font-semibold">الموظف:</span> {employee.name}
        </span>
        <span>
          <span className="font-semibold">الوصف:</span> {withdraw.desc}
        </span>
        <span>
          <span className="font-semibold">المبلغ:</span>{" "}
          {withdraw.amount.toLocaleString("ar-LY")} د.ل
        </span>
        <span>
          <span className="font-semibold">رقم السحب:</span> #{withdraw.id}
        </span>
        <span>
          <span className="font-semibold">التاريخ:</span>{" "}
          {new Date().toLocaleString("ar-EG")}
        </span>
      </div>
      <div className="flex justify-end">
        <span className="text-sm text-muted-foreground">سُحِب بواسطة النظام</span>
      </div>
    </div>
  )
);

export default EmployeeWithdrawReceipt;
