import * as React from "react";
import SectionCard from "./SectionCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Section } from "@/types";
import { Factory } from "lucide-react";
import { Calendar as CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";

type SectionsManagerProps = {
  sections: Section[];
  cashierBalance: number;
  newSectionName: string;
  onNewSectionNameChange: (value: string) => void;
  onAddSection: (e: React.FormEvent) => void;
  onDeleteSection: (id: number) => void;
  expenseInputs: { [id: number]: { desc: string; amount: string; date: Date | null } };
  onExpenseInputChange: (sectionId: number, field: "desc" | "amount" | "date", value: string | Date | null) => void;
  onAddExpense: (sectionId: number, e: React.FormEvent) => void;
  currency: (amount: number) => string;
};

const SectionsManager = ({
  sections,
  newSectionName,
  onNewSectionNameChange,
  onAddSection,
  onDeleteSection,
  expenseInputs,
  onExpenseInputChange,
  onAddExpense,
  currency,
}: SectionsManagerProps) => {
  return (
    <section className="mt-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-4">
        <h2 className="text-2xl font-bold text-primary flex items-center gap-2">
            <Factory className="h-6 w-6" />
            <span>إدارة الأقسام</span>
        </h2>
        <form className="flex items-center gap-2" onSubmit={onAddSection}>
          <Input
            className="w-44"
            type="text"
            value={newSectionName}
            onChange={e => onNewSectionNameChange(e.target.value)}
            placeholder="اسم القسم الجديد"
            required
          />
          <Button type="submit">إضافة قسم</Button>
        </form>
      </div>
      {sections.length === 0 ? (
        <div className="bg-muted rounded-lg p-8 text-center text-muted-foreground">لا توجد أقسام حتى الآن</div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {sections.map(section => (
            <div key={section.id} className="">
              <SectionCard
                section={section}
                currency={currency}
                onDeleteSection={() => onDeleteSection(section.id)}
                expenseInputs={expenseInputs[section.id] || { desc: "", amount: "", date: null }}
                onExpenseInputChange={(field, value) => onExpenseInputChange(section.id, field, value)}
                onAddExpense={(e) => onAddExpense(section.id, e)}
                extraExpenseFields={
                  <div className="flex items-center gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          type="button"
                          variant={"outline"}
                          className={`
                            w-[160px] justify-start text-left font-normal
                            ${!expenseInputs[section.id]?.date ? "text-muted-foreground" : ""}
                          `}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4 opacity-70" />
                          {expenseInputs[section.id]?.date
                            ? format(expenseInputs[section.id].date, "yyyy-MM-dd")
                            : "اختر التاريخ"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={expenseInputs[section.id]?.date || undefined}
                          onSelect={d => onExpenseInputChange(section.id, "date", d)}
                          initialFocus
                          className="p-3 pointer-events-auto"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                }
              />
            </div>
          ))}
        </div>
      )}
    </section>
  );
};

export default SectionsManager;
