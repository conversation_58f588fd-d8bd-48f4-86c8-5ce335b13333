const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const { initializeDatabase } = require('./database');
const EmployeeModel = require('./models/employees');
const CustomerModel = require('./models/customers');
const CashierModel = require('./models/cashier');
const SectionModel = require('./models/sections');

// مسار البيانات القديمة
const oldDataPath = path.join(app.getPath('userData'), 'data');

// قراءة البيانات القديمة
function readOldData(filename) {
  const filePath = path.join(oldDataPath, `${filename}.json`);
  try {
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error(`Error reading old ${filename}:`, error);
  }
  return null;
}

// ترحيل البيانات
function migrateData() {
  console.log('Starting data migration from localStorage to SQLite...');

  try {
    // تهيئة قاعدة البيانات
    initializeDatabase();

    // ترحيل رصيد الخزينة
    const cashierData = readOldData('cashier');
    if (cashierData && cashierData.balance) {
      CashierModel.updateCashierBalance(cashierData.balance);
      console.log('✓ Migrated cashier balance:', cashierData.balance);
    }

    // ترحيل إيداعات الخزينة
    const depositsData = readOldData('cashier-deposits');
    if (depositsData && Array.isArray(depositsData)) {
      depositsData.forEach(deposit => {
        try {
          CashierModel.addDeposit(
            deposit.invoiceNumber,
            deposit.customerName,
            deposit.amount,
            deposit.date
          );
          
          // تحديث حالة الدفع إذا كانت مدفوعة
          if (deposit.paidAmount > 0) {
            // سيتم تطوير هذا لاحقاً
          }
        } catch (error) {
          console.error('Error migrating deposit:', error);
        }
      });
      console.log('✓ Migrated', depositsData.length, 'deposits');
    }

    // ترحيل العملاء
    const customersData = readOldData('customers');
    if (customersData && Array.isArray(customersData)) {
      customersData.forEach(customer => {
        try {
          const newCustomer = CustomerModel.createCustomer(
            customer.name,
            customer.phone || '',
            customer.address || ''
          );

          // ترحيل مدفوعات العميل
          if (customer.payments && Array.isArray(customer.payments)) {
            customer.payments.forEach(payment => {
              CustomerModel.addCustomerPayment(
                newCustomer.id,
                payment.amount,
                payment.type,
                payment.paymentDate,
                payment.notes || ''
              );
            });
          }
        } catch (error) {
          console.error('Error migrating customer:', error);
        }
      });
      console.log('✓ Migrated', customersData.length, 'customers');
    }

    // ترحيل الأقسام
    const sectionsData = readOldData('sections');
    if (sectionsData && Array.isArray(sectionsData)) {
      sectionsData.forEach(section => {
        try {
          const newSection = SectionModel.createSection(section.name);

          // ترحيل مصروفات القسم
          if (section.expenses && Array.isArray(section.expenses)) {
            section.expenses.forEach(expense => {
              SectionModel.addSectionExpense(
                newSection.id,
                expense.desc,
                expense.amount,
                expense.date
              );
            });
          }
        } catch (error) {
          console.error('Error migrating section:', error);
        }
      });
      console.log('✓ Migrated', sectionsData.length, 'sections');
    }

    // ترحيل الموظفين
    const employeesData = readOldData('employees');
    if (employeesData && Array.isArray(employeesData)) {
      employeesData.forEach(employee => {
        try {
          const newEmployee = EmployeeModel.createEmployee(
            employee.name,
            employee.fixedSalary || 0
          );

          // تحديث الرصيد إذا كان مختلفاً عن المرتب الأساسي
          if (employee.balance !== employee.fixedSalary) {
            const balanceDiff = employee.balance - (employee.fixedSalary || 0);
            if (balanceDiff !== 0) {
              EmployeeModel.updateEmployee(newEmployee.id, {
                ...newEmployee,
                balance: employee.balance
              });
            }
          }

          // ترحيل السحوبات
          if (employee.withdraws && Array.isArray(employee.withdraws)) {
            employee.withdraws.forEach(withdraw => {
              EmployeeModel.addWithdraw(
                newEmployee.id,
                withdraw.desc,
                withdraw.amount,
                withdraw.date
              );
            });
          }

          // ترحيل الإضافات
          if (employee.additions && Array.isArray(employee.additions)) {
            employee.additions.forEach(addition => {
              EmployeeModel.addEmployeeAddition(
                newEmployee.id,
                addition.amount,
                addition.reason,
                false // عدم السداد التلقائي أثناء الترحيل
              );
            });
          }

          // ترحيل معاملات الرصيد
          if (employee.balanceTransactions && Array.isArray(employee.balanceTransactions)) {
            employee.balanceTransactions.forEach(transaction => {
              EmployeeModel.addBalanceTransaction(
                newEmployee.id,
                transaction.amount,
                transaction.notes,
                transaction.type
              );
            });
          }

          // ترحيل خصومات المرتب
          if (employee.salaryDeductions && Array.isArray(employee.salaryDeductions)) {
            employee.salaryDeductions.forEach(deduction => {
              EmployeeModel.addSalaryDeduction(
                newEmployee.id,
                deduction.amount,
                deduction.reason,
                deduction.month
              );
            });
          }

          // ترحيل سجلات السحب على المكشوف
          if (employee.overdraftRecords && Array.isArray(employee.overdraftRecords)) {
            // سيتم التعامل مع هذا بشكل خاص لأنه معقد
            console.log('Note: Overdraft records for employee', employee.name, 'need manual review');
          }

        } catch (error) {
          console.error('Error migrating employee:', error);
        }
      });
      console.log('✓ Migrated', employeesData.length, 'employees');
    }

    console.log('✅ Data migration completed successfully!');
    
    // إنشاء نسخة احتياطية من البيانات القديمة
    const backupPath = path.join(oldDataPath, 'backup-' + Date.now());
    if (fs.existsSync(oldDataPath)) {
      fs.renameSync(oldDataPath, backupPath);
      console.log('✓ Old data backed up to:', backupPath);
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

module.exports = { migrateData };
