const { db } = require('../database');

class CashierModel {
  // الحصول على رصيد الخزينة
  static getCashierBalance() {
    const result = db.prepare(`
      SELECT value FROM system_settings WHERE key = 'cashier_balance'
    `).get();

    return result ? parseFloat(result.value) : 0;
  }

  // تحديث رصيد الخزينة
  static updateCashierBalance(balance) {
    const stmt = db.prepare(`
      INSERT OR REPLACE INTO system_settings (key, value, updated_at)
      VALUES ('cashier_balance', ?, CURRENT_TIMESTAMP)
    `);
    
    stmt.run(balance.toString());
    return balance;
  }

  // إضافة مبلغ للخزينة
  static addToCashier(amount) {
    const currentBalance = this.getCashierBalance();
    const newBalance = currentBalance + amount;
    return this.updateCashierBalance(newBalance);
  }

  // خصم مبلغ من الخزينة
  static deductFromCashier(amount) {
    const currentBalance = this.getCashierBalance();
    const newBalance = currentBalance - amount;
    return this.updateCashierBalance(newBalance);
  }

  // الحصول على جميع الإيداعات
  static getAllDeposits() {
    return db.prepare(`
      SELECT * FROM cashier_deposits ORDER BY date DESC
    `).all().map(deposit => ({
      id: deposit.id,
      invoiceNumber: deposit.invoice_number,
      customerName: deposit.customer_name,
      amount: deposit.amount,
      paidAmount: deposit.paid_amount,
      remainingAmount: deposit.remaining_amount,
      status: deposit.status,
      date: deposit.date
    }));
  }

  // إضافة إيداع جديد
  static addDeposit(invoiceNumber, customerName, amount, date) {
    const stmt = db.prepare(`
      INSERT INTO cashier_deposits (invoice_number, customer_name, amount, remaining_amount, date)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(invoiceNumber, customerName, amount, amount, date);
    
    return {
      id: result.lastInsertRowid,
      invoiceNumber,
      customerName,
      amount,
      paidAmount: 0,
      remainingAmount: amount,
      status: 'pending',
      date
    };
  }

  // تحديث إيداع
  static updateDeposit(id, data) {
    const stmt = db.prepare(`
      UPDATE cashier_deposits 
      SET invoice_number = ?, customer_name = ?, amount = ?, paid_amount = ?, 
          remaining_amount = ?, status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    
    stmt.run(
      data.invoiceNumber,
      data.customerName,
      data.amount,
      data.paidAmount,
      data.remainingAmount,
      data.status,
      id
    );

    return this.getDepositById(id);
  }

  // الحصول على إيداع واحد
  static getDepositById(id) {
    const deposit = db.prepare(`
      SELECT * FROM cashier_deposits WHERE id = ?
    `).get(id);

    if (!deposit) return null;

    return {
      id: deposit.id,
      invoiceNumber: deposit.invoice_number,
      customerName: deposit.customer_name,
      amount: deposit.amount,
      paidAmount: deposit.paid_amount,
      remainingAmount: deposit.remaining_amount,
      status: deposit.status,
      date: deposit.date
    };
  }

  // حذف إيداع
  static deleteDeposit(id) {
    const stmt = db.prepare(`DELETE FROM cashier_deposits WHERE id = ?`);
    return stmt.run(id);
  }

  // دفع مبلغ من فاتورة
  static payInvoiceAmount(depositId, paidAmount) {
    const transaction = db.transaction(() => {
      const deposit = this.getDepositById(depositId);
      if (!deposit) {
        throw new Error('Deposit not found');
      }

      const newPaidAmount = deposit.paidAmount + paidAmount;
      const newRemainingAmount = deposit.amount - newPaidAmount;
      const newStatus = newRemainingAmount <= 0 ? 'completed' : 'pending';

      // تحديث الإيداع
      const updateStmt = db.prepare(`
        UPDATE cashier_deposits 
        SET paid_amount = ?, remaining_amount = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      updateStmt.run(newPaidAmount, Math.max(0, newRemainingAmount), newStatus, depositId);

      // إضافة المبلغ للخزينة
      this.addToCashier(paidAmount);

      return this.getDepositById(depositId);
    });

    return transaction();
  }
}

module.exports = CashierModel;
