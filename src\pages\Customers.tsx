import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Trash2, Edit, Plus, User, Phone, MapPin, Receipt, DollarSign, FileText, CreditCard } from "lucide-react";
import { useCustomers } from "@/hooks/useCustomers";
import { useAppContext } from "@/hooks/useAppContext";

const currency = (a: number) => a.toLocaleString("ar-LY") + " د.ل";

export default function Customers() {
  const {
    customers,
    addCustomer,
    updateCustomer,
    removeCustomer,
    calculateCustomerTotals,
    createInvoiceForCustomer,
    makePayment,
    getCustomerPayments,
    updateCustomersWithExistingInvoices
  } = useCustomers();

  const { addPaymentToCashier, cashierDeposits } = useAppContext();

  // ربط الفواتير الموجودة بالعملاء عند تحميل الصفحة (مرة واحدة فقط)
  React.useEffect(() => {
    if (cashierDeposits && cashierDeposits.length > 0 && customers.length > 0) {
      updateCustomersWithExistingInvoices(cashierDeposits);
    }
  }, [cashierDeposits.length]); // إزالة dependencies التي تسبب الحلقة

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isInvoiceDialogOpen, setIsInvoiceDialogOpen] = useState(false);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [isCustomerDetailsOpen, setIsCustomerDetailsOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<any>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  
  const [newCustomerForm, setNewCustomerForm] = useState({
    name: "",
    phone: "",
    address: ""
  });

  const [newInvoiceForm, setNewInvoiceForm] = useState({
    invoiceNumber: "",
    amount: "",
    paidAmount: "",
    remainingAmount: ""
  });

  const [paymentForm, setPaymentForm] = useState({
    amount: "",
    notes: "",
    type: "partial" as "partial" | "full"
  });

  const handleAddCustomer = async (e: React.FormEvent) => {
    e.preventDefault();
    await addCustomer(newCustomerForm.name, newCustomerForm.phone, newCustomerForm.address);
    setNewCustomerForm({ name: "", phone: "", address: "" });
    setIsAddDialogOpen(false);
  };

  const handleEditCustomer = async (e: React.FormEvent) => {
    e.preventDefault();
    if (editingCustomer) {
      await updateCustomer(editingCustomer.id, {
        name: editingCustomer.name,
        phone: editingCustomer.phone,
        address: editingCustomer.address
      });
      setIsEditDialogOpen(false);
      setEditingCustomer(null);
    }
  };

  const handleDeleteCustomer = async (id: number) => {
    if (window.confirm("هل أنت متأكد من حذف هذا العميل؟")) {
      await removeCustomer(id);
    }
  };

  const handleCreateInvoice = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCustomer) return;

    const amount = Number(newInvoiceForm.amount);
    const paidAmount = Number(newInvoiceForm.paidAmount) || 0;
    const remainingAmount = Number(newInvoiceForm.remainingAmount) || (amount - paidAmount);

    await createInvoiceForCustomer(selectedCustomer.id, {
      invoiceNumber: newInvoiceForm.invoiceNumber,
      amount,
      paidAmount,
      remainingAmount
    });

    // إضافة المبلغ المدفوع للخزينة
    if (paidAmount > 0) {
      addPaymentToCashier(paidAmount);
    }

    setNewInvoiceForm({
      invoiceNumber: "",
      amount: "",
      paidAmount: "",
      remainingAmount: ""
    });
    setIsInvoiceDialogOpen(false);
  };

  const handleMakePayment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCustomer || !selectedInvoice) return;

    const amount = Number(paymentForm.amount);

    const payment = await makePayment(
      selectedCustomer.id,
      selectedInvoice.id,
      amount,
      paymentForm.type,
      paymentForm.notes
    );

    if (payment) {
      // إضافة المبلغ للخزينة
      addPaymentToCashier(amount);
    }

    setPaymentForm({
      amount: "",
      notes: "",
      type: "partial"
    });
    setIsPaymentDialogOpen(false);
    setSelectedInvoice(null);
  };

  const openCustomerDetails = (customer: any) => {
    setSelectedCustomer(customer);
    setIsCustomerDetailsOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">إدارة العملاء</h1>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              إضافة عميل جديد
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>إضافة عميل جديد</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleAddCustomer} className="space-y-4">
              <div>
                <Label htmlFor="customerName">اسم العميل *</Label>
                <Input
                  id="customerName"
                  value={newCustomerForm.name}
                  onChange={(e) => setNewCustomerForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="أدخل اسم العميل"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="customerPhone">رقم الهاتف</Label>
                <Input
                  id="customerPhone"
                  value={newCustomerForm.phone}
                  onChange={(e) => setNewCustomerForm(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="أدخل رقم الهاتف"
                />
              </div>
              
              <div>
                <Label htmlFor="customerAddress">العنوان</Label>
                <Textarea
                  id="customerAddress"
                  value={newCustomerForm.address}
                  onChange={(e) => setNewCustomerForm(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="أدخل عنوان العميل"
                  rows={3}
                />
              </div>
              
              <div className="flex gap-2 pt-4">
                <Button type="submit" className="flex-1">إضافة العميل</Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsAddDialogOpen(false)}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* إحصائيات العملاء */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي العملاء</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customers.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الفواتير</CardTitle>
            <Badge variant="secondary" className="text-xs">فاتورة</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {customers.reduce((sum, customer) => sum + customer.totalInvoices, 0)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المدفوع</CardTitle>
            <Badge variant="default" className="text-xs">مدفوع</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {currency(customers.reduce((sum, customer) => sum + customer.totalPaid, 0))}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المتبقي</CardTitle>
            <Badge variant="destructive" className="text-xs">متبقي</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {currency(customers.reduce((sum, customer) => sum + customer.totalRemaining, 0))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* جدول العملاء */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة العملاء</CardTitle>
        </CardHeader>
        <CardContent>
          {customers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد عملاء مسجلين حتى الآن</p>
              <p className="text-sm">ابدأ بإضافة عميل جديد</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>اسم العميل</TableHead>
                  <TableHead>رقم الهاتف</TableHead>
                  <TableHead>العنوان</TableHead>
                  <TableHead>عدد الفواتير</TableHead>
                  <TableHead>إجمالي المدفوع</TableHead>
                  <TableHead>إجمالي المتبقي</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {customers.map((customer) => {
                  const totals = calculateCustomerTotals(customer.id);
                  return (
                    <TableRow key={customer.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          {customer.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          {customer.phone || "غير محدد"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          {customer.address || "غير محدد"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{totals.totalInvoices}</Badge>
                      </TableCell>
                      <TableCell className="text-green-600 font-medium">
                        {currency(totals.totalPaid)}
                      </TableCell>
                      <TableCell className="text-red-600 font-medium">
                        {currency(totals.totalRemaining)}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => openCustomerDetails(customer)}
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingCustomer(customer);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteCustomer(customer.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* حوار تعديل العميل */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>تعديل بيانات العميل</DialogTitle>
          </DialogHeader>
          {editingCustomer && (
            <form onSubmit={handleEditCustomer} className="space-y-4">
              <div>
                <Label htmlFor="editCustomerName">اسم العميل *</Label>
                <Input
                  id="editCustomerName"
                  value={editingCustomer.name}
                  onChange={(e) => setEditingCustomer(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="أدخل اسم العميل"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="editCustomerPhone">رقم الهاتف</Label>
                <Input
                  id="editCustomerPhone"
                  value={editingCustomer.phone}
                  onChange={(e) => setEditingCustomer(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="أدخل رقم الهاتف"
                />
              </div>
              
              <div>
                <Label htmlFor="editCustomerAddress">العنوان</Label>
                <Textarea
                  id="editCustomerAddress"
                  value={editingCustomer.address}
                  onChange={(e) => setEditingCustomer(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="أدخل عنوان العميل"
                  rows={3}
                />
              </div>
              
              <div className="flex gap-2 pt-4">
                <Button type="submit" className="flex-1">حفظ التغييرات</Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => {
                    setIsEditDialogOpen(false);
                    setEditingCustomer(null);
                  }}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>

      {/* نافذة تفاصيل العميل */}
      <Dialog open={isCustomerDetailsOpen} onOpenChange={setIsCustomerDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تفاصيل العميل - {selectedCustomer?.name}</DialogTitle>
          </DialogHeader>
          {selectedCustomer && (
            <Tabs defaultValue="invoices" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="invoices">الفواتير</TabsTrigger>
                <TabsTrigger value="payments">المدفوعات</TabsTrigger>
                <TabsTrigger value="info">معلومات العميل</TabsTrigger>
              </TabsList>

              <TabsContent value="invoices" className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">فواتير العميل</h3>
                  <Button
                    onClick={() => {
                      setIsInvoiceDialogOpen(true);
                    }}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    إضافة فاتورة جديدة
                  </Button>
                </div>

                {selectedCustomer.invoices?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Receipt className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>لا توجد فواتير لهذا العميل</p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>رقم الفاتورة</TableHead>
                        <TableHead>إجمالي الفاتورة</TableHead>
                        <TableHead>المبلغ المدفوع</TableHead>
                        <TableHead>المبلغ المتبقي</TableHead>
                        <TableHead>الحالة</TableHead>
                        <TableHead>الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedCustomer.invoices?.map((invoice: any) => (
                        <TableRow key={invoice.id}>
                          <TableCell className="font-medium">{invoice.invoiceNumber}</TableCell>
                          <TableCell>{currency(invoice.amount)}</TableCell>
                          <TableCell className="text-green-600">{currency(invoice.paidAmount)}</TableCell>
                          <TableCell className="text-red-600">{currency(invoice.remainingAmount)}</TableCell>
                          <TableCell>
                            {invoice.status === 'completed' ? (
                              <Badge variant="default">مكتملة</Badge>
                            ) : (
                              <Badge variant="destructive">غير مكتملة</Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {invoice.remainingAmount > 0 && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedInvoice(invoice);
                                  setPaymentForm(prev => ({
                                    ...prev,
                                    amount: invoice.remainingAmount.toString()
                                  }));
                                  setIsPaymentDialogOpen(true);
                                }}
                              >
                                <CreditCard className="h-4 w-4 mr-2" />
                                سداد
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </TabsContent>

              <TabsContent value="payments" className="space-y-4">
                <h3 className="text-lg font-semibold">سجل المدفوعات</h3>
                {getCustomerPayments(selectedCustomer.id).length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>لا توجد مدفوعات لهذا العميل</p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>المبلغ</TableHead>
                        <TableHead>نوع الدفعة</TableHead>
                        <TableHead>التاريخ</TableHead>
                        <TableHead>ملاحظات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {getCustomerPayments(selectedCustomer.id).map((payment: any) => (
                        <TableRow key={payment.id}>
                          <TableCell className="font-medium text-green-600">
                            {currency(payment.amount)}
                          </TableCell>
                          <TableCell>
                            <Badge variant={payment.type === 'full' ? 'default' : 'secondary'}>
                              {payment.type === 'full' ? 'سداد كامل' : 'سداد جزئي'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {new Date(payment.paymentDate).toLocaleDateString("ar-LY")}
                          </TableCell>
                          <TableCell>{payment.notes || '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </TabsContent>

              <TabsContent value="info" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <User className="h-4 w-4" />
                        <span className="font-medium">اسم العميل</span>
                      </div>
                      <p>{selectedCustomer.name}</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Phone className="h-4 w-4" />
                        <span className="font-medium">رقم الهاتف</span>
                      </div>
                      <p>{selectedCustomer.phone || 'غير محدد'}</p>
                    </CardContent>
                  </Card>

                  <Card className="col-span-2">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <MapPin className="h-4 w-4" />
                        <span className="font-medium">العنوان</span>
                      </div>
                      <p>{selectedCustomer.address || 'غير محدد'}</p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>

      {/* نافذة إضافة فاتورة جديدة */}
      <Dialog open={isInvoiceDialogOpen} onOpenChange={setIsInvoiceDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>إضافة فاتورة جديدة - {selectedCustomer?.name}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleCreateInvoice} className="space-y-4">
            <div>
              <Label htmlFor="invoiceNumber">رقم الفاتورة *</Label>
              <Input
                id="invoiceNumber"
                value={newInvoiceForm.invoiceNumber}
                onChange={(e) => setNewInvoiceForm(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                placeholder="أدخل رقم الفاتورة"
                required
              />
            </div>

            <div>
              <Label htmlFor="amount">إجمالي الفاتورة *</Label>
              <Input
                id="amount"
                type="number"
                min="0"
                step="0.01"
                value={newInvoiceForm.amount}
                onChange={(e) => setNewInvoiceForm(prev => ({ ...prev, amount: e.target.value }))}
                placeholder="أدخل إجمالي مبلغ الفاتورة"
                required
              />
            </div>

            <div>
              <Label htmlFor="paidAmount">المبلغ المدفوع</Label>
              <Input
                id="paidAmount"
                type="number"
                min="0"
                step="0.01"
                value={newInvoiceForm.paidAmount}
                onChange={(e) => setNewInvoiceForm(prev => ({ ...prev, paidAmount: e.target.value }))}
                placeholder="أدخل المبلغ المدفوع"
              />
            </div>

            <div>
              <Label htmlFor="remainingAmount">المبلغ المتبقي</Label>
              <Input
                id="remainingAmount"
                type="number"
                min="0"
                step="0.01"
                value={newInvoiceForm.remainingAmount}
                onChange={(e) => setNewInvoiceForm(prev => ({ ...prev, remainingAmount: e.target.value }))}
                placeholder="أدخل المبلغ المتبقي"
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button type="submit" className="flex-1">إضافة الفاتورة</Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsInvoiceDialogOpen(false)}
                className="flex-1"
              >
                إلغاء
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* نافذة السداد */}
      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              سداد فاتورة رقم {selectedInvoice?.invoiceNumber}
            </DialogTitle>
          </DialogHeader>
          {selectedInvoice && (
            <form onSubmit={handleMakePayment} className="space-y-4">
              <div className="bg-muted p-4 rounded-lg">
                <p><strong>المبلغ المتبقي:</strong> {currency(selectedInvoice.remainingAmount)}</p>
              </div>

              <div>
                <Label htmlFor="paymentAmount">مبلغ الدفعة *</Label>
                <Input
                  id="paymentAmount"
                  type="number"
                  min="0"
                  max={selectedInvoice.remainingAmount}
                  step="0.01"
                  value={paymentForm.amount}
                  onChange={(e) => {
                    const amount = Number(e.target.value);
                    setPaymentForm(prev => ({
                      ...prev,
                      amount: e.target.value,
                      type: amount >= selectedInvoice.remainingAmount ? 'full' : 'partial'
                    }));
                  }}
                  placeholder="أدخل مبلغ الدفعة"
                  required
                />
              </div>

              <div>
                <Label htmlFor="paymentNotes">ملاحظات</Label>
                <Textarea
                  id="paymentNotes"
                  value={paymentForm.notes}
                  onChange={(e) => setPaymentForm(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="أدخل ملاحظات إضافية"
                  rows={3}
                />
              </div>

              <div className="flex gap-2 pt-4">
                <Button type="submit" className="flex-1">
                  تسجيل الدفعة
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsPaymentDialogOpen(false);
                    setSelectedInvoice(null);
                  }}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
