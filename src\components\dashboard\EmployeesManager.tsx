
import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Employee, Withdraw } from "@/types";
import { Users, Plus, Trash2, <PERSON>er, Wallet, TrendingUp } from "lucide-react";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";

import EmployeeWithdrawReceipt from "./EmployeeWithdrawReceipt";
import { Calendar as CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";

type EmployeesManagerProps = {
  employees: Employee[];
  newWithdraws: { [id: number]: { desc: string; amount: string; date: Date | null } };
  onNewWithdrawChange: (
    empId: number,
    field: "desc" | "amount" | "date",
    value: string | Date | null
  ) => void;
  onAddWithdraw: (empId: number, e: React.FormEvent) => void;
  currency: (amount: number) => string;
  addEmployee: (name: string, balance: number, maxLimit: number, fixedSalary: number) => void;
  removeEmployee: (id: number) => void;
  getEmployeeMonthlyWithdraws: (empId: number, year: number, month: number) => Withdraw[];
  getEmployeeTotalDeposits: (empId: number) => number;
  getEmployeeTotalWithdraws: (empId: number) => number;
  getEmployeeTotalAdditions: (empId: number) => number;
  getEmployeeAvailableBalance: (empId: number) => number;
  getEmployeeTotalOverdraft: (empId: number) => number;
  addEmployeeAddition: (empId: number, amount: number, reason: string, autoPayOverdraft?: boolean) => void;
  payOverdraftAmount: (empId: number, overdraftId: number, amount: number) => void;
};

const EmployeesManager = ({
  employees,
  newWithdraws,
  onNewWithdrawChange,
  onAddWithdraw,
  currency,
  addEmployee,
  removeEmployee,
  getEmployeeMonthlyWithdraws,
  getEmployeeTotalDeposits,
  getEmployeeTotalWithdraws,
  getEmployeeTotalAdditions,
  getEmployeeAvailableBalance,
  getEmployeeTotalOverdraft,
  addEmployeeAddition,
  payOverdraftAmount,
}: EmployeesManagerProps) => {
  const [open, setOpen] = React.useState(false);
  const [newEmpName, setNewEmpName] = React.useState("");
  const [newEmpFixedSalary, setNewEmpFixedSalary] = React.useState("");
  const [selectedEmpId, setSelectedEmpId] = React.useState<number>(0);

  // حالة حوار إضافة المبالغ
  const [additionDialogOpen, setAdditionDialogOpen] = React.useState(false);
  const [additionAmount, setAdditionAmount] = React.useState("");
  const [additionReason, setAdditionReason] = React.useState("");
  const [autoPayOverdraft, setAutoPayOverdraft] = React.useState(false);

  // حالة حوار السداد على دفعات
  const [overdraftDialogOpen, setOverdraftDialogOpen] = React.useState(false);
  const [paymentAmount, setPaymentAmount] = React.useState("");
  const [selectedOverdraftId, setSelectedOverdraftId] = React.useState<number>(0);
  const [monthlyReportOpen, setMonthlyReportOpen] = React.useState(false);
  const [reportEmpId, setReportEmpId] = React.useState<number>(0);
  const [reportDate, setReportDate] = React.useState<Date>(new Date());

  const [receiptState, setReceiptState] = React.useState<{
    open: boolean;
    employee?: Employee;
    withdraw?: Withdraw;
  }>({ open: false });

  // حالة إظهار/إخفاء بيانات الموظفين
  const [visibleEmployees, setVisibleEmployees] = React.useState<Set<number>>(new Set());

  const lastWithdrawRef = React.useRef<{ [empId: number]: number }>({});

  // دالة لتبديل إظهار/إخفاء بيانات الموظف
  const toggleEmployeeVisibility = (empId: number) => {
    setVisibleEmployees(prev => {
      const newSet = new Set(prev);
      if (newSet.has(empId)) {
        newSet.delete(empId);
      } else {
        newSet.add(empId);
      }
      return newSet;
    });
  };

  const handleAdd = (empId: number, e: React.FormEvent) => {
    e.preventDefault();
    const emp = employees.find((e) => e.id === empId);
    const prevWithdraws = emp?.withdraws || [];
    onAddWithdraw(empId, e);

    setTimeout(() => {
      const updatedEmp = employees.find((e) => e.id === empId);
      if (updatedEmp && updatedEmp.withdraws.length > prevWithdraws.length) {
        const newW = updatedEmp.withdraws[updatedEmp.withdraws.length - 1];
        setReceiptState({ open: true, employee: updatedEmp, withdraw: newW });
        lastWithdrawRef.current[empId] = newW.id;
      }
    }, 100);
  };

  const printRef = React.useRef<HTMLDivElement>(null);
  const handlePrint = () => {
    if (printRef.current) {
      const printContent = printRef.current.innerHTML;
      const win = window.open("", "_blank", "width=400,height=600");
      if (win) {
        win.document.write(
          `<html><head><title>وصل السحب</title><link rel="stylesheet" href="/index.css"/></head><body dir="rtl">${printContent}</body></html>`
        );
        win.document.close();
        win.focus();
        setTimeout(() => {
          win.print();
        }, 500);
      }
    }
  };

  const handleAddition = () => {
    const amount = Number(additionAmount);
    if (amount > 0 && additionReason.trim() && selectedEmpId) {
      addEmployeeAddition(selectedEmpId, amount, additionReason.trim(), autoPayOverdraft);
      setAdditionAmount("");
      setAdditionReason("");
      setAutoPayOverdraft(false);
      setAdditionDialogOpen(false);
      setSelectedEmpId(0);
    }
  };

  const handleOverdraftPayment = () => {
    const amount = Number(paymentAmount);
    if (amount > 0 && selectedOverdraftId && selectedEmpId) {
      payOverdraftAmount(selectedEmpId, selectedOverdraftId, amount);
      setPaymentAmount("");
      setSelectedOverdraftId(0);
      setOverdraftDialogOpen(false);
      setSelectedEmpId(0);
    }
  };

  const getCurrentMonthWithdraws = (empId: number) => {
    const now = new Date();
    return getEmployeeMonthlyWithdraws(empId, now.getFullYear(), now.getMonth());
  };

  const getMonthlyTotal = (empId: number, date: Date) => {
    const withdraws = getEmployeeMonthlyWithdraws(empId, date.getFullYear(), date.getMonth());
    return withdraws.reduce((total, w) => total + w.amount, 0);
  };

  return (
    <Card className="mt-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-6 w-6" />
          <span>سحوبات الموظفين</span>
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Button size="sm" variant="default" className="ml-auto" title="إضافة موظف جديد">
                <Plus className="w-4 h-4" /> إضافة موظف
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>إضافة موظف جديد</DialogTitle>
              </DialogHeader>
              <form onSubmit={e => {
                e.preventDefault();
                addEmployee(newEmpName, 0, 0, Number(newEmpFixedSalary) || 0);
                setNewEmpName("");
                setNewEmpFixedSalary("");
                setOpen(false);
              }} className="space-y-4">
                <Input
                  autoFocus
                  placeholder="اسم الموظف *"
                  value={newEmpName}
                  onChange={e => setNewEmpName(e.target.value)}
                  required
                />
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="المرتب الأساسي *"
                  value={newEmpFixedSalary}
                  onChange={e => setNewEmpFixedSalary(e.target.value)}
                  required
                />
                <DialogFooter>
                  <Button type="submit">إضافة الموظف</Button>
                  <DialogClose asChild>
                    <Button type="button" variant="outline">إلغاء</Button>
                  </DialogClose>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {employees.length === 0 ? (
            <div className="text-center text-muted-foreground">لا يوجد موظفين حاليا.</div>
          ) : (
            employees.map(emp => {
              const currentMonthWithdraws = getCurrentMonthWithdraws(emp.id);
              const currentMonthTotal = currentMonthWithdraws.reduce((total, w) => total + w.amount, 0);
              const totalDeposits = getEmployeeTotalDeposits(emp.id);
              const totalWithdraws = getEmployeeTotalWithdraws(emp.id);
              const totalAdditions = getEmployeeTotalAdditions(emp.id);
              const availableBalance = getEmployeeAvailableBalance(emp.id);
              const totalOverdraft = getEmployeeTotalOverdraft(emp.id);
              const isVisible = visibleEmployees.has(emp.id);

              return (
                <div key={emp.id} className={`border rounded-lg p-4 space-y-3 ${
                  emp.balance < 0
                    ? "border-red-300 bg-red-50/50"
                    : "border-muted bg-muted/40"
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex flex-col">
                      <button
                        onClick={() => toggleEmployeeVisibility(emp.id)}
                        className="font-semibold text-lg text-right hover:text-blue-600 transition-colors cursor-pointer"
                        title="انقر لإظهار/إخفاء البيانات"
                      >
                        {emp.name} {isVisible ? '▼' : '◀'}
                      </button>
                      {isVisible && (
                        <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground mt-2">
                          <span className="text-blue-600 font-medium">المرتب الأساسي: {currency(emp.fixedSalary || 0)}</span>
                          <span className="text-green-600">إجمالي الإضافات: {currency(totalAdditions)}</span>
                          <span className="text-red-600">إجمالي السحوبات: {currency(totalWithdraws)}</span>
                          {totalOverdraft > 0 && (
                            <span className="text-orange-600 font-medium">السحب على المكشوف: {currency(totalOverdraft)}</span>
                          )}
                          <span className="text-purple-600 font-medium">الرصيد المتاح: {currency(availableBalance)}</span>
                          <span>سحوبات هذا الشهر: {currency(currentMonthTotal)}</span>
                        </div>
                      )}
                    </div>
                    {isVisible && (
                      <div className="flex gap-2">
                        <Button
                          size="icon"
                          variant="outline"
                          onClick={() => {
                            setSelectedEmpId(emp.id);
                            setAdditionDialogOpen(true);
                          }}
                          title="إضافة مبلغ (علاوة، مكافأة، إلخ)"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                        {totalOverdraft > 0 && (
                          <Button
                            size="icon"
                            variant="outline"
                            onClick={() => {
                              setSelectedEmpId(emp.id);
                              setOverdraftDialogOpen(true);
                            }}
                            title="سداد السحب على المكشوف"
                            className="border-orange-300 text-orange-600 hover:bg-orange-50"
                          >
                            <Wallet className="w-4 h-4" />
                          </Button>
                        )}
                        <Button
                          size="icon"
                          variant="outline"
                          onClick={() => {
                            setReportEmpId(emp.id);
                            setMonthlyReportOpen(true);
                          }}
                          title="التقرير الشهري"
                        >
                          <TrendingUp className="w-4 h-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="destructive"
                          onClick={() => removeEmployee(emp.id)}
                          title="حذف الموظف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    )}
                  </div>

                  {isVisible && (
                    <>
                      {/* Progress bar for current balance */}
                      <div className="w-full bg-muted rounded-full h-2 relative">
                        {emp.balance < 0 ? (
                          // شريط أحمر للرصيد السالب
                          <div className="h-2 rounded-full bg-red-500 w-full animate-pulse" />
                        ) : (
                          <div
                            className="h-2 rounded-full transition-all bg-green-500"
                            style={{
                              width: `${Math.min(Math.max((emp.balance / Math.max(emp.fixedSalary + totalAdditions, emp.balance)) * 100, 0), 100)}%`
                            }}
                          />
                        )}
                      </div>

                  {/* عرض تفاصيل السحب على المكشوف */}
                  {totalOverdraft > 0 && emp.overdraftRecords && (
                    <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-md">
                      <h4 className="text-sm font-medium text-orange-800 mb-2">تفاصيل السحب على المكشوف:</h4>
                      <div className="space-y-1">
                        {emp.overdraftRecords
                          .filter(record => record.status !== 'fully_paid')
                          .map(record => (
                            <div key={record.id} className="text-xs text-orange-700 flex justify-between">
                              <span>السحب رقم {record.withdrawId}</span>
                              <span>{currency(record.remainingAmount)} من أصل {currency(record.amount)}</span>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    {emp.withdraws.length === 0 ? (
                      <div className="text-sm text-muted-foreground">لا توجد سحوبات.</div>
                    ) : (
                      emp.withdraws.slice(-5).map(w => (
                        <div
                          key={w.id}
                          className="flex justify-between items-center bg-muted p-2 px-4 rounded"
                        >
                          <span className="text-sm text-muted-foreground">{w.desc}</span>
                          <span className="font-mono">{currency(w.amount)}</span>
                          <Dialog
                            open={
                              receiptState.open &&
                              receiptState.employee?.id === emp.id &&
                              receiptState.withdraw?.id === w.id
                            }
                            onOpenChange={o => setReceiptState(s => o ? { ...s, open: true, employee: emp, withdraw: w } : { open: false })}
                          >
                            <DialogTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                title="طباعة وصل"
                                onClick={() => setReceiptState({ open: true, employee: emp, withdraw: w })}
                              >
                                <Printer className="w-4 h-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>وصل السحب</DialogTitle>
                              </DialogHeader>
                              <div ref={printRef}>
                                <EmployeeWithdrawReceipt employee={emp} withdraw={w} />
                              </div>
                              <DialogFooter>
                                <Button onClick={handlePrint} variant="default">
                                  طباعة الوصل
                                </Button>
                                <DialogClose asChild>
                                  <Button variant="outline">إغلاق</Button>
                                </DialogClose>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </div>
                      ))
                    )}
                    {emp.withdraws.length > 5 && (
                      <div className="text-center text-sm text-muted-foreground">
                        ... و {emp.withdraws.length - 5} سحوبات أخرى
                      </div>
                    )}
                  </div>

                  <form
                    className="flex flex-col sm:flex-row gap-2 items-center pt-3"
                    onSubmit={e => handleAdd(emp.id, e)}
                  >
                    <Input
                      className="flex-1"
                      type="text"
                      value={newWithdraws[emp.id]?.desc || ""}
                      onChange={e => onNewWithdrawChange(emp.id, "desc", e.target.value)}
                      placeholder="وصف السحبة"
                    />
                    <Input
                      className="w-28"
                      type="number"
                      min={1}
                      value={newWithdraws[emp.id]?.amount || ""}
                      onChange={e => onNewWithdrawChange(emp.id, "amount", e.target.value)}
                      placeholder={`المبلغ (الرصيد: ${currency(emp.balance)})`}
                    />
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          type="button"
                          variant={"outline"}
                          className={`
                            w-[140px] justify-start text-left font-normal
                            ${!newWithdraws[emp.id]?.date ? "text-muted-foreground" : ""}
                          `}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4 opacity-70" />
                          {newWithdraws[emp.id]?.date
                            ? format(newWithdraws[emp.id].date, "yyyy-MM-dd")
                            : "اختر التاريخ"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={newWithdraws[emp.id]?.date || undefined}
                          onSelect={d => onNewWithdrawChange(emp.id, "date", d)}
                          initialFocus
                          className="p-3 pointer-events-auto"
                        />
                      </PopoverContent>
                    </Popover>
                    <Button
                      size="sm"
                      type="submit"
                    >
                      إضافة سحب
                    </Button>
                  </form>
                    </>
                  )}
                </div>
              );
            })
          )}
        </div>

        {/* Addition Dialog */}
        <Dialog open={additionDialogOpen} onOpenChange={setAdditionDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>إضافة مبلغ للموظف</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Input
                type="number"
                min="0"
                step="0.01"
                placeholder="المبلغ المراد إضافته"
                value={additionAmount}
                onChange={e => setAdditionAmount(e.target.value)}
              />
              <Input
                placeholder="سبب الإضافة (علاوة، مكافأة، إلخ)"
                value={additionReason}
                onChange={e => setAdditionReason(e.target.value)}
              />

              {selectedEmpId && (() => {
                const employee = employees.find(e => e.id === selectedEmpId);
                const totalOverdraft = getEmployeeTotalOverdraft(selectedEmpId);

                return totalOverdraft > 0 && (
                  <div className="space-y-3">
                    <div className="p-3 bg-orange-50 border border-orange-200 rounded-md">
                      <p className="text-sm text-orange-800">
                        <strong>تنبيه:</strong> هذا الموظف لديه سحب على المكشوف بقيمة {currency(totalOverdraft)}
                      </p>
                    </div>

                    <div className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="checkbox"
                        id="autoPayOverdraft"
                        checked={autoPayOverdraft}
                        onChange={e => setAutoPayOverdraft(e.target.checked)}
                        className="rounded border-gray-300"
                      />
                      <label htmlFor="autoPayOverdraft" className="text-sm font-medium">
                        سداد السحب على المكشوف تلقائياً من هذا المبلغ
                      </label>
                    </div>

                    {autoPayOverdraft && Number(additionAmount) > 0 && (
                      <div className="p-2 bg-blue-50 border border-blue-200 rounded-md text-sm">
                        <p><strong>توزيع المبلغ:</strong></p>
                        <p>• سداد السحب على المكشوف: {currency(Math.min(Number(additionAmount), totalOverdraft))}</p>
                        <p>• إضافة للرصيد: {currency(Math.max(0, Number(additionAmount) - totalOverdraft))}</p>
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>
            <DialogFooter>
              <Button onClick={handleAddition}>إضافة المبلغ</Button>
              <DialogClose asChild>
                <Button variant="outline">إلغاء</Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Overdraft Payment Dialog */}
        <Dialog open={overdraftDialogOpen} onOpenChange={setOverdraftDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>سداد السحب على المكشوف</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              {selectedEmpId && (() => {
                const employee = employees.find(e => e.id === selectedEmpId);
                const overdraftRecords = employee?.overdraftRecords?.filter(r => r.status !== 'fully_paid') || [];

                return (
                  <>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">اختر السحب المراد سداده:</label>
                      <select
                        className="w-full p-2 border rounded-md"
                        value={selectedOverdraftId}
                        onChange={e => setSelectedOverdraftId(Number(e.target.value))}
                      >
                        <option value={0}>اختر السحب...</option>
                        {overdraftRecords.map(record => (
                          <option key={record.id} value={record.id}>
                            {`السحب رقم ${record.withdrawId} - المتبقي: ${currency(record.remainingAmount)}`}
                          </option>
                        ))}
                      </select>
                    </div>

                    {selectedOverdraftId > 0 && (() => {
                      const selectedRecord = overdraftRecords.find(r => r.id === selectedOverdraftId);
                      return selectedRecord && (
                        <div className="p-3 bg-orange-50 rounded-md">
                          <p className="text-sm"><strong>المبلغ الأصلي:</strong> {currency(selectedRecord.amount)}</p>
                          <p className="text-sm"><strong>المبلغ المتبقي:</strong> {currency(selectedRecord.remainingAmount)}</p>
                          <p className="text-sm"><strong>التاريخ:</strong> {new Date(selectedRecord.date).toLocaleDateString("ar-LY")}</p>
                          <p className="text-sm"><strong>الحالة:</strong> {
                            selectedRecord.status === 'pending' ? 'في الانتظار' :
                            selectedRecord.status === 'partially_paid' ? 'مدفوع جزئياً' : 'مدفوع بالكامل'
                          }</p>
                        </div>
                      );
                    })()}

                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      max={selectedOverdraftId > 0 ? overdraftRecords.find(r => r.id === selectedOverdraftId)?.remainingAmount : undefined}
                      placeholder="مبلغ السداد"
                      value={paymentAmount}
                      onChange={e => setPaymentAmount(e.target.value)}
                    />

                    {employee && Number(paymentAmount) > employee.balance && (
                      <div className="p-2 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-600">
                          تحذير: رصيد الموظف ({currency(employee.balance)}) غير كافٍ للسداد
                        </p>
                      </div>
                    )}
                  </>
                );
              })()}
            </div>
            <DialogFooter>
              <Button
                onClick={handleOverdraftPayment}
                disabled={!selectedOverdraftId || !paymentAmount || Number(paymentAmount) <= 0}
              >
                سداد المبلغ
              </Button>
              <DialogClose asChild>
                <Button variant="outline">إلغاء</Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Monthly Report Dialog */}
        <Dialog open={monthlyReportOpen} onOpenChange={setMonthlyReportOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>التقرير الشهري للموظف</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {format(reportDate, "yyyy-MM")}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={reportDate}
                    onSelect={d => d && setReportDate(d)}
                    initialFocus
                    className="p-3 pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
              
              {reportEmpId > 0 && (() => {
                const emp = employees.find(e => e.id === reportEmpId);
                const monthlyWithdraws = getEmployeeMonthlyWithdraws(reportEmpId, reportDate.getFullYear(), reportDate.getMonth());
                const total = getMonthlyTotal(reportEmpId, reportDate);
                
                return (
                  <div className="space-y-4">
                    <div className="text-lg font-semibold">
                      تقرير {emp?.name} - {format(reportDate, "yyyy/MM")}
                    </div>
                    <div className="bg-muted p-4 rounded">
                      <div>إجمالي السحوبات: {currency(total)}</div>
                      <div>عدد السحوبات: {monthlyWithdraws.length}</div>
                    </div>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {monthlyWithdraws.length === 0 ? (
                        <div className="text-center text-muted-foreground">لا توجد سحوبات في هذا الشهر</div>
                      ) : (
                        monthlyWithdraws.map(w => (
                          <div key={w.id} className="flex justify-between items-center bg-background p-2 rounded">
                            <span>{w.desc}</span>
                            <span>{currency(w.amount)}</span>
                            <span className="text-sm text-muted-foreground">
                              {new Date(w.date).toLocaleDateString("ar-LY")}
                            </span>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                );
              })()}
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">إغلاق</Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>

      </CardContent>

      {receiptState.open && receiptState.employee && receiptState.withdraw && (
        <Dialog
          open={receiptState.open}
          onOpenChange={open => setReceiptState(open ? receiptState : { open: false })}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>وصل السحب</DialogTitle>
            </DialogHeader>
            <div ref={printRef}>
              <EmployeeWithdrawReceipt
                employee={receiptState.employee}
                withdraw={receiptState.withdraw}
              />
            </div>
            <DialogFooter>
              <Button onClick={handlePrint} variant="default">
                طباعة الوصل
              </Button>
              <DialogClose asChild>
                <Button variant="outline">إغلاق</Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
};

export default EmployeesManager;
