
import * as React from "react";
import EmployeesManager from "@/components/dashboard/EmployeesManager";
import { useAppContext } from "@/hooks/useAppContext";

const Employees = () => {
  const {
    employees,
    newWithdraws,
    handleNewWithdrawChange,
    handleAddWithdraw,
    currency,
    addEmployee,
    removeEmployee,
    getEmployeeMonthlyWithdraws,
    getEmployeeTotalDeposits,
    getEmployeeTotalWithdraws,
    getEmployeeTotalAdditions,
    getEmployeeAvailableBalance,
    getEmployeeTotalOverdraft,
    addEmployeeAddition,
    payOverdraftAmount,
  } = useAppContext();

  return (
    <EmployeesManager
      employees={employees}
      newWithdraws={newWithdraws}
      onNewWithdrawChange={handleNewWithdrawChange}
      onAddWithdraw={handleAddWithdraw}
      currency={currency}
      addEmployee={addEmployee}
      removeEmployee={removeEmployee}
      getEmployeeMonthlyWithdraws={getEmployeeMonthlyWithdraws}
      getEmployeeTotalDeposits={getEmployeeTotalDeposits}
      getEmployeeTotalWithdraws={getEmployeeTotalWithdraws}
      getEmployeeTotalAdditions={getEmployeeTotalAdditions}
      getEmployeeAvailableBalance={getEmployeeAvailableBalance}
      getEmployeeTotalOverdraft={getEmployeeTotalOverdraft}
      addEmployeeAddition={addEmployeeAddition}
      payOverdraftAmount={payOverdraftAmount}
    />
  );
};

export default Employees;
