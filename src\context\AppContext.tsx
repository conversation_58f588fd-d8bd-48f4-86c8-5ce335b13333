
import React, { createContext, ReactNode } from "react";
import { AppContextType } from "@/types";
import { useCashier } from "@/hooks/useCashier";
import { useSections } from "@/hooks/useSections";
import { useEmployees } from "@/hooks/useEmployees";
import { useCustomers } from "@/hooks/useCustomers";

const currency = (a: number) => a.toLocaleString("ar-LY") + " د.ل";

export const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider = ({ children }: { children: ReactNode }) => {
  const cashierHook = useCashier();
  const sectionsHook = useSections();
  const employeesHook = useEmployees();
  const customersHook = useCustomers();

  const contextValue: AppContextType = {
    // Cashier
    cashierBalance: cashierHook.cashierBalance,
    cashierDeposits: cashierHook.cashierDeposits,
    deposit: cashierHook.deposit,
    setDeposit: cashierHook.setDeposit,
    handleDeposit: cashierHook.handleDeposit,
    handleAdvancedDeposit: cashierHook.handleAdvancedDeposit,
    addPaymentToCashier: cashierHook.addPaymentToCashier,
    
    // Sections
    sections: sectionsHook.sections,
    newSectionName: sectionsHook.newSectionName,
    setNewSectionName: sectionsHook.setNewSectionName,
    handleAddSection: sectionsHook.handleAddSection,
    handleDeleteSection: sectionsHook.handleDeleteSection,
    expenseInputs: sectionsHook.expenseInputs,
    handleExpenseInputChange: sectionsHook.handleExpenseInputChange,
    handleAddExpense: (sectionId: number, e: React.FormEvent) => 
      sectionsHook.handleAddExpense(sectionId, e, cashierHook.cashierBalance, cashierHook.deductFromCashier),
    
    // Employees
    employees: employeesHook.employees,
    newWithdraws: employeesHook.newWithdraws,
    handleNewWithdrawChange: employeesHook.handleNewWithdrawChange,
    handleAddWithdraw: (empId: number, e: React.FormEvent) => 
      employeesHook.handleAddWithdraw(empId, e, cashierHook.cashierBalance, cashierHook.deductFromCashier),
    addEmployee: employeesHook.addEmployee,
    removeEmployee: employeesHook.removeEmployee,
    updateEmployeeBalance: employeesHook.updateEmployeeBalance,
    getEmployeeMonthlyWithdraws: employeesHook.getEmployeeMonthlyWithdraws,
    getEmployeeTotalDeposits: employeesHook.getEmployeeTotalDeposits,
    getEmployeeTotalWithdraws: employeesHook.getEmployeeTotalWithdraws,
    getEmployeeTotalAdditions: employeesHook.getEmployeeTotalAdditions,
    getEmployeeAvailableBalance: employeesHook.getEmployeeAvailableBalance,
    getEmployeeTotalOverdraft: employeesHook.getEmployeeTotalOverdraft,
    addEmployeeAddition: (empId: number, amount: number, reason: string, autoPayOverdraft: boolean = false) =>
      employeesHook.addEmployeeAddition(empId, amount, reason, cashierHook.deductFromCashier, autoPayOverdraft),
    payOverdraftAmount: employeesHook.payOverdraftAmount,
    addSalaryDeduction: employeesHook.addSalaryDeduction,

    // Customers
    customers: customersHook.customers,
    addCustomer: customersHook.addCustomer,
    updateCustomer: customersHook.updateCustomer,
    removeCustomer: customersHook.removeCustomer,
    getCustomerInvoices: customersHook.getCustomerInvoices,
    createInvoiceForCustomer: customersHook.createInvoiceForCustomer,
    makePayment: customersHook.makePayment,
    getCustomerPayments: customersHook.getCustomerPayments,
    updateInvoiceStatus: customersHook.updateInvoiceStatus,
    updateCustomersWithExistingInvoices: customersHook.updateCustomersWithExistingInvoices,

    // Utility
    currency,
  };

  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
};
