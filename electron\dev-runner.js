const { spawn } = require('child_process');
const { createServer } = require('vite');

async function startDev() {
  // بدء خادم Vite
  const server = await createServer({
    server: {
      port: 8080
    }
  });
  
  await server.listen();
  console.log('Vite server started on http://localhost:8080');

  // انتظار قليل للتأكد من بدء الخادم
  setTimeout(() => {
    // بدء Electron
    const electronProcess = spawn('electron', ['.'], {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'development'
      }
    });

    electronProcess.on('close', () => {
      server.close();
      process.exit();
    });
  }, 2000);
}

startDev().catch(console.error);
