
import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { CashierDeposit } from "@/types";
import { useElectronAPI } from "./useElectronAPI";

const currency = (a: number) => a.toLocaleString("ar-LY") + " د.ل";

export const useCashier = () => {
  const [cashierBalance, setCashierBalance] = useState(0);
  const [deposit, setDeposit] = useState("");
  const [cashierDeposits, setCashierDeposits] = useState<CashierDeposit[]>([]);
  const { isElectron, api } = useElectronAPI();

  // تحميل رصيد الخزينة والإيداعات عند بدء التطبيق
  useEffect(() => {
    const loadCashierData = async () => {
      if (isElectron && api) {
        try {
          const balance = await api.getCashierBalance();
          setCashierBalance(balance);

          const deposits = await api.getCashierDeposits();
          setCashierDeposits(deposits);
        } catch (error) {
          console.error('Error loading cashier data:', error);
        }
      } else {
        // استخدام localStorage كبديل للمتصفح
        const savedBalance = localStorage.getItem('cashierBalance');
        if (savedBalance) {
          setCashierBalance(Number(savedBalance));
        }

        const savedDeposits = localStorage.getItem('cashierDeposits');
        if (savedDeposits) {
          setCashierDeposits(JSON.parse(savedDeposits));
        }
      }
    };

    loadCashierData();
  }, [isElectron, api]);

  // حفظ رصيد الخزينة
  const saveCashierBalance = async (newBalance: number) => {
    if (isElectron && api) {
      try {
        await api.updateCashierBalance(newBalance);
      } catch (error) {
        console.error('Error saving cashier balance:', error);
      }
    } else {
      localStorage.setItem('cashierBalance', newBalance.toString());
    }
  };

  const handleDeposit = async (e: React.FormEvent) => {
    e.preventDefault();
    const val = Number(deposit);
    if (val > 0) {
      const newBalance = cashierBalance + val;
      setCashierBalance(newBalance);
      await saveCashierBalance(newBalance);
      toast({ title: "تم الإيداع", description: `تمت إضافة ${currency(val)} للخزينة.` });
      setDeposit("");
    } else {
      toast({ title: "تحذير", description: "يجب إدخال مبلغ صحيح.", variant: "destructive" });
    }
  };

  const deductFromCashier = async (amount: number) => {
    const newBalance = cashierBalance - amount;
    setCashierBalance(newBalance);
    await saveCashierBalance(newBalance);
  };

  // إيداع متقدم مع تفاصيل الفاتورة (يسجل فقط المبلغ المدفوع)
  const handleAdvancedDeposit = async (depositData: Omit<CashierDeposit, 'id' | 'date' | 'status'>) => {
    if (depositData.amount <= 0) {
      toast({ title: "تحذير", description: "يجب إدخال مبلغ صحيح.", variant: "destructive" });
      return;
    }

    if (!depositData.invoiceNumber.trim()) {
      toast({ title: "تحذير", description: "يجب إدخال رقم الفاتورة.", variant: "destructive" });
      return;
    }

    if (!depositData.customerName.trim()) {
      toast({ title: "تحذير", description: "يجب إدخال اسم العميل.", variant: "destructive" });
      return;
    }

    // تحديد حالة الفاتورة
    const status = depositData.remainingAmount > 0 ? 'pending' : 'completed';
    const depositWithStatus = { ...depositData, status };

    if (isElectron && api) {
      try {
        const newDeposit = await api.addAdvancedDeposit(depositWithStatus);
        setCashierDeposits([...cashierDeposits, newDeposit]);
        // إضافة المبلغ المدفوع فقط للخزينة (ليس إجمالي الفاتورة)
        setCashierBalance(prev => prev + depositData.paidAmount);
      } catch (error) {
        console.error('Error adding advanced deposit:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء إضافة الإيداع.", variant: "destructive" });
        return;
      }
    } else {
      const newDeposit: CashierDeposit = {
        id: cashierDeposits.length === 0 ? 1 : Math.max(...cashierDeposits.map(d => d.id)) + 1,
        ...depositWithStatus,
        date: new Date().toISOString()
      };

      const newDeposits = [...cashierDeposits, newDeposit];
      setCashierDeposits(newDeposits);
      localStorage.setItem('cashierDeposits', JSON.stringify(newDeposits));

      // إضافة المبلغ المدفوع فقط للخزينة
      const newBalance = cashierBalance + depositData.paidAmount;
      setCashierBalance(newBalance);
      await saveCashierBalance(newBalance);
    }

    toast({
      title: "تم الإيداع",
      description: `تمت إضافة فاتورة رقم ${depositData.invoiceNumber}. المبلغ المضاف للخزينة: ${currency(depositData.paidAmount)}`
    });
  };

  // إضافة مبلغ للخزينة عند السداد
  const addPaymentToCashier = async (amount: number) => {
    const newBalance = cashierBalance + amount;
    setCashierBalance(newBalance);
    await saveCashierBalance(newBalance);
  };

  return {
    cashierBalance,
    cashierDeposits,
    deposit,
    setDeposit,
    handleDeposit,
    handleAdvancedDeposit,
    addPaymentToCashier,
    deductFromCashier,
  };
};
