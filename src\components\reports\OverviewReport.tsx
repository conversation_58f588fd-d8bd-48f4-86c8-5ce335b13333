
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Calculator, Printer, Users, Building2 } from "lucide-react";
import { Employee, Section } from "@/types";

interface OverviewReportProps {
  employees: Employee[];
  sections: Section[];
  cashierBalance: number;
  currency: (amount: number) => string;
}

const OverviewReport = ({ employees, sections, cashierBalance, currency }: OverviewReportProps) => {
  // حساب الإحصائيات العامة
  const totalExpenses = sections.reduce((sum, section) => 
    sum + section.expenses.reduce((expSum, exp) => expSum + exp.amount, 0), 0
  );
  
  const totalWithdrawals = employees.reduce((sum, emp) => 
    sum + emp.withdraws.reduce((wSum, w) => wSum + w.amount, 0), 0
  );
  
  const totalEmployeeBalances = employees.reduce((sum, emp) => sum + emp.balance, 0);

  const handlePrintOverview = () => {
    const printContent = document.getElementById('overview-report');
    if (printContent) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>التقرير المالي العام</title>
              <style>
                body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .summary { margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 8px; }
                .stat-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
                .stat-card { padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
                .print-date { text-align: left; margin-bottom: 20px; font-size: 12px; color: #666; }
              </style>
            </head>
            <body>
              <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString("ar-LY")}</div>
              ${printContent.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <Calculator className="w-6 h-6" />
          <CardTitle>التقرير المالي العام</CardTitle>
        </div>
        <Button onClick={handlePrintOverview} variant="outline" size="sm">
          <Printer className="w-4 h-4 mr-2" />
          طباعة النظرة العامة
        </Button>
      </CardHeader>
      <CardContent>
        <div id="overview-report">
          <div className="header">
            <h2 className="text-2xl font-bold">التقرير المالي العام</h2>
            <p className="text-gray-600">ملخص شامل للوضع المالي</p>
          </div>

          <div className="summary">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="stat-card bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <Calculator className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">رصيد الخزينة</p>
                    <p className="text-lg font-semibold text-blue-600">{currency(cashierBalance)}</p>
                  </div>
                </div>
              </div>

              <div className="stat-card bg-red-50 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <Building2 className="w-5 h-5 text-red-600" />
                  <div>
                    <p className="text-sm text-gray-600">إجمالي مصروفات الأقسام</p>
                    <p className="text-lg font-semibold text-red-600">{currency(totalExpenses)}</p>
                  </div>
                </div>
              </div>

              <div className="stat-card bg-orange-50 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-orange-600" />
                  <div>
                    <p className="text-sm text-gray-600">إجمالي سحوبات الموظفين</p>
                    <p className="text-lg font-semibold text-orange-600">{currency(totalWithdrawals)}</p>
                  </div>
                </div>
              </div>

              <div className="stat-card bg-green-50 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600">أرصدة الموظفين</p>
                    <p className="text-lg font-semibold text-green-600">{currency(totalEmployeeBalances)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Users className="w-5 h-5" />
                ملخص الموظفين ({employees.length})
              </h3>
              {employees.length === 0 ? (
                <p className="text-gray-500">لا يوجد موظفين</p>
              ) : (
                <div className="space-y-2">
                  {employees.map(emp => (
                    <div key={emp.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <span className="font-medium">{emp.name}</span>
                      <span className="text-sm">{currency(emp.balance)}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Building2 className="w-5 h-5" />
                ملخص الأقسام ({sections.length})
              </h3>
              {sections.length === 0 ? (
                <p className="text-gray-500">لا يوجد أقسام</p>
              ) : (
                <div className="space-y-2">
                  {sections.map(section => {
                    const sectionTotal = section.expenses.reduce((sum, exp) => sum + exp.amount, 0);
                    return (
                      <div key={section.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span className="font-medium">{section.name}</span>
                        <span className="text-sm text-red-600">{currency(sectionTotal)}</span>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OverviewReport;
