
/* نظام التصميم العام */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    direction: rtl;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --primary: 24 95% 53%; /* لون برتقالي */
    --primary-foreground: 0 0% 100%;
    --secondary: 215 29% 28%; /* لون أزرق داكن */
    --secondary-foreground: 0 0% 100%;
    --card: 0 0% 100%;
    --muted: 220 14% 96%;
    --accent: 24 95% 53%; /* لون برتقالي للتأكيد */
    --border: 215 16% 85%;
    --input: 215 16% 91%;
    --radius: 0.5rem;
  }
  body {
    @apply bg-background text-foreground;
    font-family: '<PERSON><PERSON>wal', sans-serif;
  }
}

.btn-main {
  @apply text-white bg-primary hover:bg-primary/90 transition px-4 py-2 rounded font-bold;
}

.btn-secondary {
  @apply text-white bg-secondary hover:bg-secondary/90 transition px-4 py-2 rounded font-bold;
}

.table-head {
  @apply bg-primary text-white text-sm;
}

.table-row {
  @apply bg-white even:bg-muted hover:bg-accent/20 transition;
}

/* Removed the custom .card class */

