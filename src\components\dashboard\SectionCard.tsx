import * as React from "react";
import { Section } from "@/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type SectionCardProps = {
  section: Section;
  currency: (amount: number) => string;
  onDeleteSection: () => void;
  expenseInputs: { desc: string; amount: string; date: Date | null };
  onExpenseInputChange: (field: "desc" | "amount" | "date", value: string | Date | null) => void;
  onAddExpense: (e: React.FormEvent) => void;
  extraExpenseFields?: React.ReactNode;
};

const SectionCard = ({
  section,
  currency,
  onDeleteSection,
  expenseInputs,
  onExpenseInputChange,
  onAddExpense,
  extraExpenseFields
}: SectionCardProps) => {
  return (
    <div className="bg-card p-4 rounded-lg shadow-sm flex flex-col gap-2">
      <div className="flex items-center justify-between">
        <span className="text-lg font-bold">{section.name}</span>
        <Button variant="destructive" onClick={onDeleteSection} size="sm">
          حذف القسم
        </Button>
      </div>
      {/* قائمة المصروفات */}
      <div className="space-y-2">
        {section.expenses.length === 0 ? (
          <div className="text-sm text-muted-foreground">لا توجد مصروفات في هذا القسم.</div>
        ) : (
          section.expenses.map(exp => (
            <div key={exp.id} className="flex justify-between bg-muted px-2 py-1 rounded">
              <div>{exp.desc}</div>
              <div className="font-mono">{currency(exp.amount)}</div>
              <div className="text-xs text-muted-foreground">{new Date(exp.date).toLocaleDateString("ar-LY")}</div>
            </div>
          ))
        )}
      </div>
      {/* إضافة مصروف */}
      <form className="flex flex-col md:flex-row gap-2 items-center pt-2" onSubmit={onAddExpense}>
        <Input
          className="flex-1"
          type="text"
          value={expenseInputs?.desc || ""}
          onChange={e => onExpenseInputChange("desc", e.target.value)}
          placeholder="وصف المصروف"
        />
        <Input
          className="w-24"
          type="number"
          min={1}
          value={expenseInputs?.amount || ""}
          onChange={e => onExpenseInputChange("amount", e.target.value)}
          placeholder="المبلغ"
        />
        {extraExpenseFields ? extraExpenseFields : null}
        <Button size="sm" type="submit">
          إضافة مصروف
        </Button>
      </form>
    </div>
  );
};

export default SectionCard;
