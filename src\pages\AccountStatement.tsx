
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {

  TrendingUp,
  TrendingDown,
  Users,
  Receipt,
  CalendarIcon,
  FileText,
  CreditCard,
  Wallet,
  Building2,
  Calculator,
  Printer,
  User
} from "lucide-react";
import { format } from "date-fns";
import { useAppContext } from "@/hooks/useAppContext";

const currency = (a: number) => a.toLocaleString("ar-LY") + " د.ل";

export default function AccountStatement() {
  const {
    cashierBalance,
    cashierDeposits,
    customers,
    getCustomerPayments,
    sections,
    employees
  } = useAppContext();



  const [dateRange, setDateRange] = useState({
    from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    to: new Date()
  });

  // حساب الإحصائيات العامة
  const totalCustomers = customers.length;
  const totalInvoices = cashierDeposits.length;
  const totalInvoiceAmount = cashierDeposits.reduce((sum, deposit) => sum + deposit.amount, 0);
  const totalPaidAmount = cashierDeposits.reduce((sum, deposit) => sum + deposit.paidAmount, 0);
  const totalRemainingAmount = cashierDeposits.reduce((sum, deposit) => sum + deposit.remainingAmount, 0);

  // حساب إجمالي مصروفات الأقسام
  const totalSectionExpenses = sections.reduce((sum, section) =>
    sum + section.expenses.reduce((expSum, expense) => expSum + expense.amount, 0), 0
  );

  // حساب إجمالي سحوبات الموظفين
  const totalEmployeeWithdraws = employees.reduce((sum, employee) =>
    sum + employee.withdraws.reduce((wSum, withdraw) => wSum + withdraw.amount, 0), 0
  );

  // حساب إجمالي الإضافات للموظفين
  const totalEmployeeAdditions = employees.reduce((sum, employee) =>
    sum + (employee.additions || []).reduce((aSum, addition) => aSum + addition.amount, 0), 0
  );

  // حساب إجمالي السحب على المكشوف
  const totalEmployeeOverdraft = employees.reduce((sum, employee) =>
    sum + (employee.overdraftRecords || [])
      .filter(record => record.status !== 'fully_paid')
      .reduce((oSum, record) => oSum + record.remainingAmount, 0), 0
  );

  // فلترة البيانات حسب التاريخ
  const filteredDeposits = cashierDeposits.filter(deposit => {
    const depositDate = new Date(deposit.date);
    return depositDate >= dateRange.from && depositDate <= dateRange.to;
  });

  // جمع جميع المدفوعات
  const allPayments = customers.flatMap(customer =>
    getCustomerPayments(customer.id).map(payment => ({
      ...payment,
      customerName: customer.name
    }))
  );

  const filteredPayments = allPayments.filter(payment => {
    const paymentDate = new Date(payment.paymentDate);
    return paymentDate >= dateRange.from && paymentDate <= dateRange.to;
  });

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">كشف الحساب الشامل</h1>

        {/* فلتر التاريخ */}
        <div className="flex gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                من: {format(dateRange.from, "yyyy-MM-dd")}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dateRange.from}
                onSelect={(date) => date && setDateRange(prev => ({ ...prev, from: date }))}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <CalendarIcon className="mr-2 h-4 w-4" />
                إلى: {format(dateRange.to, "yyyy-MM-dd")}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dateRange.to}
                onSelect={(date) => date && setDateRange(prev => ({ ...prev, to: date }))}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* الإحصائيات العامة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Wallet className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">رصيد الخزينة</p>
                <p className="text-2xl font-bold text-blue-600">{currency(cashierBalance)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Users className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">إجمالي العملاء</p>
                <p className="text-2xl font-bold text-green-600">{totalCustomers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Receipt className="h-8 w-8 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">إجمالي الفواتير</p>
                <p className="text-2xl font-bold text-purple-600">{totalInvoices}</p>
              </div>
            </div>
          </CardContent>
        </Card>


      </div>

      {/* التفاصيل المالية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              الإيرادات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
              <span>إجمالي قيمة الفواتير</span>
              <span className="font-bold text-green-600">{currency(totalInvoiceAmount)}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-green-100 rounded-lg">
              <span>المبالغ المحصلة</span>
              <span className="font-bold text-green-700">{currency(totalPaidAmount)}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
              <span>المبالغ المستحقة</span>
              <span className="font-bold text-yellow-600">{currency(totalRemainingAmount)}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingDown className="h-5 w-5 text-red-600" />
              المصروفات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
              <span>مصروفات الأقسام</span>
              <span className="font-bold text-red-600">{currency(totalSectionExpenses)}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-red-100 rounded-lg">
              <span>سحوبات الموظفين</span>
              <span className="font-bold text-red-700">{currency(totalEmployeeWithdraws)}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-red-200 rounded-lg">
              <span>إجمالي المصروفات</span>
              <span className="font-bold text-red-800">
                {currency(totalSectionExpenses + totalEmployeeWithdraws)}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* تفاصيل المعاملات */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Calculator className="w-4 h-4" />
            نظرة عامة
          </TabsTrigger>
          <TabsTrigger value="invoices" className="flex items-center gap-2">
            <Receipt className="w-4 h-4" />
            الفواتير
          </TabsTrigger>
          <TabsTrigger value="payments" className="flex items-center gap-2">
            <CreditCard className="w-4 h-4" />
            المدفوعات
          </TabsTrigger>
          <TabsTrigger value="customers" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            العملاء
          </TabsTrigger>
          <TabsTrigger value="employees" className="flex items-center gap-2">
            <User className="w-4 h-4" />
            الموظفين
          </TabsTrigger>
          <TabsTrigger value="sections" className="flex items-center gap-2">
            <Building2 className="w-4 h-4" />
            الأقسام
          </TabsTrigger>
        </TabsList>

        {/* النظرة العامة */}
        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-2">
                <Calculator className="w-6 h-6" />
                <CardTitle>التقرير المالي العام</CardTitle>
              </div>
              <Button
                onClick={() => {
                  const printContent = document.getElementById('overview-report');
                  if (printContent) {
                    const printWindow = window.open('', '_blank');
                    if (printWindow) {
                      printWindow.document.write(`
                        <html>
                          <head>
                            <title>التقرير المالي العام</title>
                            <style>
                              body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                              table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                              th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                              th { background-color: #f2f2f2; }
                              .header { text-align: center; margin-bottom: 30px; }
                              .summary { margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 8px; }
                              .print-date { text-align: left; margin-bottom: 20px; font-size: 12px; color: #666; }
                            </style>
                          </head>
                          <body>
                            <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString("ar-LY")}</div>
                            ${printContent.innerHTML}
                          </body>
                        </html>
                      `);
                      printWindow.document.close();
                      printWindow.print();
                    }
                  }
                }}
                variant="outline"
                size="sm"
              >
                <Printer className="w-4 h-4 mr-2" />
                طباعة النظرة العامة
              </Button>
            </CardHeader>
            <CardContent>
              <div id="overview-report">
                <div className="header">
                  <h2 className="text-2xl font-bold">التقرير المالي العام</h2>
                  <p className="text-gray-600">ملخص شامل للوضع المالي</p>
                </div>

                <div className="summary">
                  <h3 className="text-lg font-semibold mb-4">الملخص المالي</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{currency(cashierBalance)}</div>
                      <div className="text-sm text-blue-600">رصيد الخزينة</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{currency(totalPaidAmount)}</div>
                      <div className="text-sm text-green-600">إجمالي المحصل</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{currency(totalSectionExpenses + totalEmployeeWithdraws)}</div>
                      <div className="text-sm text-red-600">إجمالي المصروفات</div>
                    </div>

                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">إحصائيات العملاء</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>عدد العملاء:</span>
                          <span className="font-bold">{totalCustomers}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>إجمالي الفواتير:</span>
                          <span className="font-bold">{totalInvoices}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>المبالغ المستحقة:</span>
                          <span className="font-bold text-red-600">{currency(totalRemainingAmount)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">إحصائيات الموظفين</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>عدد الموظفين:</span>
                          <span className="font-bold">{employees.length}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>إجمالي السحوبات:</span>
                          <span className="font-bold text-red-600">{currency(totalEmployeeWithdraws)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>إجمالي الأرصدة:</span>
                          <span className="font-bold text-green-600">
                            {currency(employees.reduce((sum, emp) => sum + emp.balance, 0))}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">إحصائيات الأقسام</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>عدد الأقسام:</span>
                          <span className="font-bold">{sections.length}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>إجمالي المصروفات:</span>
                          <span className="font-bold text-red-600">{currency(totalSectionExpenses)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>متوسط مصروفات القسم:</span>
                          <span className="font-bold">
                            {sections.length > 0 ? currency(totalSectionExpenses / sections.length) : currency(0)}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invoices" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>سجل الفواتير</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredDeposits.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>لا توجد فواتير في الفترة المحددة</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم الفاتورة</TableHead>
                      <TableHead>العميل</TableHead>
                      <TableHead>إجمالي الفاتورة</TableHead>
                      <TableHead>المبلغ المدفوع</TableHead>
                      <TableHead>المبلغ المتبقي</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>التاريخ</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDeposits.map((deposit) => (
                      <TableRow key={deposit.id}>
                        <TableCell className="font-medium">{deposit.invoiceNumber}</TableCell>
                        <TableCell>{deposit.customerName}</TableCell>
                        <TableCell>{currency(deposit.amount)}</TableCell>
                        <TableCell className="text-green-600">{currency(deposit.paidAmount)}</TableCell>
                        <TableCell className="text-red-600">{currency(deposit.remainingAmount)}</TableCell>
                        <TableCell>
                          {deposit.status === 'completed' ? (
                            <Badge variant="default">مكتملة</Badge>
                          ) : (
                            <Badge variant="destructive">غير مكتملة</Badge>
                          )}
                        </TableCell>
                        <TableCell>{new Date(deposit.date).toLocaleDateString("ar-LY")}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>سجل المدفوعات</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredPayments.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>لا توجد مدفوعات في الفترة المحددة</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>العميل</TableHead>
                      <TableHead>المبلغ</TableHead>
                      <TableHead>نوع الدفعة</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead>ملاحظات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPayments.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell className="font-medium">{payment.customerName}</TableCell>
                        <TableCell className="text-green-600 font-medium">
                          {currency(payment.amount)}
                        </TableCell>
                        <TableCell>
                          <Badge variant={payment.type === 'full' ? 'default' : 'secondary'}>
                            {payment.type === 'full' ? 'سداد كامل' : 'سداد جزئي'}
                          </Badge>
                        </TableCell>
                        <TableCell>{new Date(payment.paymentDate).toLocaleDateString("ar-LY")}</TableCell>
                        <TableCell>{payment.notes || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>تقرير العملاء</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>اسم العميل</TableHead>
                    <TableHead>عدد الفواتير</TableHead>
                    <TableHead>إجمالي المبالغ</TableHead>
                    <TableHead>المبلغ المدفوع</TableHead>
                    <TableHead>المبلغ المتبقي</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {customers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell className="font-medium">{customer.name}</TableCell>
                      <TableCell>{customer.totalInvoices}</TableCell>
                      <TableCell>{currency(customer.invoices?.reduce((sum, inv) => sum + inv.amount, 0) || 0)}</TableCell>
                      <TableCell className="text-green-600">{currency(customer.totalPaid)}</TableCell>
                      <TableCell className="text-red-600">{currency(customer.totalRemaining)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* تقارير الموظفين */}
        <TabsContent value="employees" className="space-y-4">
          {employees.length === 0 ? (
            <Card>
              <CardContent className="py-8">
                <div className="text-center text-gray-500">
                  <User className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>لا يوجد موظفين لعرض تقاريرهم</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            employees.map(employee => {
              const totalWithdrawals = employee.withdraws.reduce((sum, w) => sum + w.amount, 0);
              const totalAdditions = (employee.additions || []).reduce((sum, a) => sum + a.amount, 0);
              const totalOverdraft = (employee.overdraftRecords || [])
                .filter(record => record.status !== 'fully_paid')
                .reduce((sum, record) => sum + record.remainingAmount, 0);
              const totalOverdraftPaid = (employee.overdraftRecords || [])
                .reduce((sum, record) => sum + (record.amount - record.remainingAmount), 0);

              return (
                <Card key={employee.id} className="w-full">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                      <User className="w-5 h-5" />
                      <CardTitle>تقرير الموظف: {employee.name}</CardTitle>
                    </div>
                    <Button
                      onClick={() => {
                        const printContent = document.getElementById(`employee-report-${employee.id}`);
                        if (printContent) {
                          const printWindow = window.open('', '_blank');
                          if (printWindow) {
                            printWindow.document.write(`
                              <html>
                                <head>
                                  <title>تقرير الموظف - ${employee.name}</title>
                                  <style>
                                    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                                    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                                    th { background-color: #f2f2f2; }
                                    .header { text-align: center; margin-bottom: 30px; }
                                    .summary { margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 8px; }
                                    .print-date { text-align: left; margin-bottom: 20px; font-size: 12px; color: #666; }
                                  </style>
                                </head>
                                <body>
                                  <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString("ar-LY")}</div>
                                  ${printContent.innerHTML}
                                </body>
                              </html>
                            `);
                            printWindow.document.close();
                            printWindow.print();
                          }
                        }
                      }}
                      variant="outline"
                      size="sm"
                    >
                      <Printer className="w-4 h-4 mr-2" />
                      طباعة
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <div id={`employee-report-${employee.id}`}>
                      <div className="header">
                        <h2 className="text-2xl font-bold">تقرير مالي مفصل</h2>
                        <h3 className="text-xl">الموظف: {employee.name}</h3>
                      </div>

                      <div className="summary">
                        <h3 className="text-lg font-semibold mb-4">الملخص المالي</h3>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                          <div className="text-center p-3 bg-blue-50 rounded">
                            <div className="text-xl font-bold text-blue-600">{currency(employee.balance)}</div>
                            <div className="text-sm text-blue-600">الرصيد الحالي</div>
                          </div>
                          <div className="text-center p-3 bg-purple-50 rounded">
                            <div className="text-xl font-bold text-purple-600">{currency(employee.fixedSalary || 0)}</div>
                            <div className="text-sm text-purple-600">المرتب الأساسي</div>
                          </div>
                          <div className="text-center p-3 bg-green-50 rounded">
                            <div className="text-xl font-bold text-green-600">{currency(totalAdditions)}</div>
                            <div className="text-sm text-green-600">إجمالي الإضافات</div>
                          </div>
                          <div className="text-center p-3 bg-red-50 rounded">
                            <div className="text-xl font-bold text-red-600">{currency(totalWithdrawals)}</div>
                            <div className="text-sm text-red-600">إجمالي السحوبات</div>
                          </div>
                          <div className="text-center p-3 bg-orange-50 rounded">
                            <div className="text-xl font-bold text-orange-600">{currency(totalOverdraft)}</div>
                            <div className="text-sm text-orange-600">السحب على المكشوف</div>
                          </div>
                          <div className="text-center p-3 bg-teal-50 rounded">
                            <div className="text-xl font-bold text-teal-600">{currency(totalOverdraftPaid)}</div>
                            <div className="text-sm text-teal-600">المسدد من المكشوف</div>
                          </div>
                        </div>
                      </div>

                      {employee.withdraws.length > 0 && (
                        <div className="mb-6">
                          <h4 className="text-lg font-semibold mb-3">سجل السحوبات</h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>التاريخ</TableHead>
                                <TableHead>الوصف</TableHead>
                                <TableHead>المبلغ</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {employee.withdraws.slice(-10).map(withdraw => (
                                <TableRow key={withdraw.id}>
                                  <TableCell>{new Date(withdraw.date).toLocaleDateString("ar-LY")}</TableCell>
                                  <TableCell>{withdraw.desc}</TableCell>
                                  <TableCell className="text-red-600 font-medium">{currency(withdraw.amount)}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                          {employee.withdraws.length > 10 && (
                            <p className="text-sm text-muted-foreground mt-2 text-center">
                              عرض آخر 10 سحوبات من أصل {employee.withdraws.length}
                            </p>
                          )}
                        </div>
                      )}

                      {(employee.additions || []).length > 0 && (
                        <div className="mb-6">
                          <h4 className="text-lg font-semibold mb-3">سجل الإضافات (العلاوات والمكافآت)</h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>التاريخ</TableHead>
                                <TableHead>السبب</TableHead>
                                <TableHead>المبلغ</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {(employee.additions || []).slice(-10).map(addition => (
                                <TableRow key={addition.id}>
                                  <TableCell>{new Date(addition.date).toLocaleDateString("ar-LY")}</TableCell>
                                  <TableCell>{addition.reason}</TableCell>
                                  <TableCell className="text-green-600 font-medium">{currency(addition.amount)}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                          {(employee.additions || []).length > 10 && (
                            <p className="text-sm text-muted-foreground mt-2 text-center">
                              عرض آخر 10 إضافات من أصل {(employee.additions || []).length}
                            </p>
                          )}
                        </div>
                      )}

                      {(employee.overdraftRecords || []).length > 0 && (
                        <div className="mb-6">
                          <h4 className="text-lg font-semibold mb-3">سجل السحب على المكشوف</h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>التاريخ</TableHead>
                                <TableHead>رقم السحبة</TableHead>
                                <TableHead>المبلغ الأصلي</TableHead>
                                <TableHead>المبلغ المسدد</TableHead>
                                <TableHead>المبلغ المتبقي</TableHead>
                                <TableHead>الحالة</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {(employee.overdraftRecords || []).map(record => (
                                <TableRow key={record.id}>
                                  <TableCell>{new Date(record.date).toLocaleDateString("ar-LY")}</TableCell>
                                  <TableCell>#{record.withdrawId}</TableCell>
                                  <TableCell className="text-orange-600 font-medium">{currency(record.amount)}</TableCell>
                                  <TableCell className="text-green-600">{currency(record.amount - record.remainingAmount)}</TableCell>
                                  <TableCell className="text-red-600 font-medium">{currency(record.remainingAmount)}</TableCell>
                                  <TableCell>
                                    <Badge variant={
                                      record.status === 'fully_paid' ? 'default' :
                                      record.status === 'partially_paid' ? 'secondary' : 'destructive'
                                    }>
                                      {record.status === 'pending' ? 'في الانتظار' :
                                       record.status === 'partially_paid' ? 'مدفوع جزئياً' : 'مدفوع بالكامل'}
                                    </Badge>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      )}

                      {(employee.overdraftRecords || []).some(record => record.payments.length > 0) && (
                        <div className="mb-6">
                          <h4 className="text-lg font-semibold mb-3">سجل مدفوعات السحب على المكشوف</h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>التاريخ</TableHead>
                                <TableHead>رقم السحبة</TableHead>
                                <TableHead>مبلغ الدفعة</TableHead>
                                <TableHead>مصدر الدفعة</TableHead>
                                <TableHead>ملاحظات</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {(employee.overdraftRecords || [])
                                .flatMap(record =>
                                  record.payments.map(payment => ({
                                    ...payment,
                                    withdrawId: record.withdrawId
                                  }))
                                )
                                .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                                .slice(0, 10)
                                .map(payment => (
                                  <TableRow key={`${payment.withdrawId}-${payment.id}`}>
                                    <TableCell>{new Date(payment.date).toLocaleDateString("ar-LY")}</TableCell>
                                    <TableCell>#{payment.withdrawId}</TableCell>
                                    <TableCell className="text-green-600 font-medium">{currency(payment.amount)}</TableCell>
                                    <TableCell>
                                      <Badge variant={payment.source === 'addition' ? 'default' : 'secondary'}>
                                        {payment.source === 'addition' ? 'سداد تلقائي' : 'سداد يدوي'}
                                      </Badge>
                                    </TableCell>
                                    <TableCell>{payment.notes || '-'}</TableCell>
                                  </TableRow>
                                ))}
                            </TableBody>
                          </Table>
                        </div>
                      )}

                      {employee.balanceTransactions.length > 0 && (
                        <div>
                          <h4 className="text-lg font-semibold mb-3">سجل معاملات الرصيد</h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>التاريخ</TableHead>
                                <TableHead>النوع</TableHead>
                                <TableHead>المبلغ</TableHead>
                                <TableHead>الملاحظات</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {employee.balanceTransactions.slice(-10).map(transaction => (
                                <TableRow key={transaction.id}>
                                  <TableCell>{new Date(transaction.date).toLocaleDateString("ar-LY")}</TableCell>
                                  <TableCell>
                                    <Badge variant={transaction.type === 'deposit' ? 'default' : 'destructive'}>
                                      {transaction.type === 'deposit' ? 'إيداع' : 'سحب'}
                                    </Badge>
                                  </TableCell>
                                  <TableCell className={transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'}>
                                    {currency(transaction.amount)}
                                  </TableCell>
                                  <TableCell>{transaction.notes || '-'}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </TabsContent>

        {/* تقارير الأقسام */}
        <TabsContent value="sections" className="space-y-4">
          {sections.length === 0 ? (
            <Card>
              <CardContent className="py-8">
                <div className="text-center text-gray-500">
                  <Building2 className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>لا يوجد أقسام لعرض تقاريرها</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            sections.map(section => {
              const totalExpenses = section.expenses.reduce((sum, exp) => sum + exp.amount, 0);
              const sortedExpenses = [...section.expenses].sort((a, b) =>
                new Date(b.date).getTime() - new Date(a.date).getTime()
              );

              return (
                <Card key={section.id} className="w-full">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="w-5 h-5" />
                      <CardTitle>تقرير القسم: {section.name}</CardTitle>
                    </div>
                    <Button
                      onClick={() => {
                        const printContent = document.getElementById(`section-report-${section.id}`);
                        if (printContent) {
                          const printWindow = window.open('', '_blank');
                          if (printWindow) {
                            printWindow.document.write(`
                              <html>
                                <head>
                                  <title>تقرير القسم - ${section.name}</title>
                                  <style>
                                    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                                    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                                    th { background-color: #f2f2f2; }
                                    .header { text-align: center; margin-bottom: 30px; }
                                    .summary { margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 8px; }
                                    .print-date { text-align: left; margin-bottom: 20px; font-size: 12px; color: #666; }
                                  </style>
                                </head>
                                <body>
                                  <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString("ar-LY")}</div>
                                  ${printContent.innerHTML}
                                </body>
                              </html>
                            `);
                            printWindow.document.close();
                            printWindow.print();
                          }
                        }
                      }}
                      variant="outline"
                      size="sm"
                    >
                      <Printer className="w-4 h-4 mr-2" />
                      طباعة
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <div id={`section-report-${section.id}`}>
                      <div className="header">
                        <h2 className="text-2xl font-bold">تقرير مصروفات مفصل</h2>
                        <h3 className="text-xl">القسم: {section.name}</h3>
                      </div>

                      <div className="summary">
                        <h3 className="text-lg font-semibold mb-4">ملخص المصروفات</h3>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          <div className="text-center p-3 bg-red-50 rounded">
                            <div className="text-xl font-bold text-red-600">{currency(totalExpenses)}</div>
                            <div className="text-sm text-red-600">إجمالي المصروفات</div>
                          </div>
                          <div className="text-center p-3 bg-blue-50 rounded">
                            <div className="text-xl font-bold text-blue-600">{section.expenses.length}</div>
                            <div className="text-sm text-blue-600">عدد المصروفات</div>
                          </div>
                          <div className="text-center p-3 bg-green-50 rounded">
                            <div className="text-xl font-bold text-green-600">
                              {section.expenses.length > 0 ? currency(totalExpenses / section.expenses.length) : currency(0)}
                            </div>
                            <div className="text-sm text-green-600">متوسط المصروف</div>
                          </div>
                        </div>
                      </div>

                      {section.expenses.length > 0 && (
                        <div>
                          <h4 className="text-lg font-semibold mb-3">تفاصيل المصروفات</h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>التاريخ</TableHead>
                                <TableHead>الوصف</TableHead>
                                <TableHead>المبلغ</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {sortedExpenses.slice(0, 15).map(expense => (
                                <TableRow key={expense.id}>
                                  <TableCell>{new Date(expense.date).toLocaleDateString("ar-LY")}</TableCell>
                                  <TableCell>{expense.desc}</TableCell>
                                  <TableCell className="text-red-600 font-medium">{currency(expense.amount)}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                          {section.expenses.length > 15 && (
                            <p className="text-sm text-muted-foreground mt-2 text-center">
                              عرض آخر 15 مصروف من أصل {section.expenses.length}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </TabsContent>

        <TabsContent value="summary" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* ملخص الفترة المحددة */}
            <Card>
              <CardHeader>
                <CardTitle>ملخص الفترة المحددة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>عدد الفواتير:</span>
                  <span className="font-bold">{filteredDeposits.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>إجمالي قيمة الفواتير:</span>
                  <span className="font-bold">{currency(filteredDeposits.reduce((sum, d) => sum + d.amount, 0))}</span>
                </div>
                <div className="flex justify-between">
                  <span>المبالغ المحصلة:</span>
                  <span className="font-bold text-green-600">
                    {currency(filteredDeposits.reduce((sum, d) => sum + d.paidAmount, 0))}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>عدد المدفوعات:</span>
                  <span className="font-bold">{filteredPayments.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>إجمالي المدفوعات:</span>
                  <span className="font-bold text-green-600">
                    {currency(filteredPayments.reduce((sum, p) => sum + p.amount, 0))}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* ملخص الموظفين */}
            <Card>
              <CardHeader>
                <CardTitle>ملخص الموظفين</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>عدد الموظفين:</span>
                  <span className="font-bold">{employees.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>إجمالي الأرصدة:</span>
                  <span className="font-bold text-green-600">
                    {currency(employees.reduce((sum, emp) => sum + emp.balance, 0))}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>إجمالي السحوبات:</span>
                  <span className="font-bold text-red-600">{currency(totalEmployeeWithdraws)}</span>
                </div>
                <div className="flex justify-between">
                  <span>إجمالي الإضافات:</span>
                  <span className="font-bold text-green-600">{currency(totalEmployeeAdditions)}</span>
                </div>
                <div className="flex justify-between">
                  <span>إجمالي المرتبات الثابتة:</span>
                  <span className="font-bold">
                    {currency(employees.reduce((sum, emp) => sum + (emp.fixedSalary || 0), 0))}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>السحب على المكشوف:</span>
                  <span className="font-bold text-orange-600">{currency(totalEmployeeOverdraft)}</span>
                </div>
                <div className="flex justify-between">
                  <span>متوسط الرصيد:</span>
                  <span className="font-bold">
                    {employees.length > 0 ? currency(employees.reduce((sum, emp) => sum + emp.balance, 0) / employees.length) : currency(0)}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* ملخص الأقسام */}
            <Card>
              <CardHeader>
                <CardTitle>ملخص الأقسام</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>عدد الأقسام:</span>
                  <span className="font-bold">{sections.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>إجمالي المصروفات:</span>
                  <span className="font-bold text-red-600">{currency(totalSectionExpenses)}</span>
                </div>
                <div className="flex justify-between">
                  <span>متوسط مصروفات القسم:</span>
                  <span className="font-bold">
                    {sections.length > 0 ? currency(totalSectionExpenses / sections.length) : currency(0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>إجمالي المعاملات:</span>
                  <span className="font-bold">
                    {sections.reduce((sum, section) => sum + section.expenses.length, 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>أكثر الأقسام إنفاقاً:</span>
                  <span className="font-bold text-sm">
                    {sections.length > 0 ?
                      sections.reduce((max, section) => {
                        const sectionTotal = section.expenses.reduce((sum, exp) => sum + exp.amount, 0);
                        const maxTotal = max.expenses.reduce((sum, exp) => sum + exp.amount, 0);
                        return sectionTotal > maxTotal ? section : max;
                      }).name : '-'
                    }
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* نسب الأداء */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>نسب الأداء المالي</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>نسبة التحصيل:</span>
                    <span className="font-bold">
                      {totalInvoiceAmount > 0 ? ((totalPaidAmount / totalInvoiceAmount) * 100).toFixed(1) : 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${totalInvoiceAmount > 0 ? (totalPaidAmount / totalInvoiceAmount) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>نسبة المستحقات:</span>
                    <span className="font-bold text-red-600">
                      {totalInvoiceAmount > 0 ? ((totalRemainingAmount / totalInvoiceAmount) * 100).toFixed(1) : 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-red-600 h-2 rounded-full"
                      style={{ width: `${totalInvoiceAmount > 0 ? (totalRemainingAmount / totalInvoiceAmount) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>نسبة المصروفات من الإيرادات:</span>
                    <span className="font-bold text-orange-600">
                      {totalPaidAmount > 0 ? (((totalSectionExpenses + totalEmployeeWithdraws) / totalPaidAmount) * 100).toFixed(1) : 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-orange-600 h-2 rounded-full"
                      style={{ width: `${totalPaidAmount > 0 ? ((totalSectionExpenses + totalEmployeeWithdraws) / totalPaidAmount) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>التوزيع المالي</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>الإيرادات المحصلة</span>
                    <span className="font-bold text-green-600">{currency(totalPaidAmount)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>مصروفات الأقسام</span>
                    <span className="font-bold text-red-600">{currency(totalSectionExpenses)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>سحوبات الموظفين</span>
                    <span className="font-bold text-red-600">{currency(totalEmployeeWithdraws)}</span>
                  </div>
                  <hr />

                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
