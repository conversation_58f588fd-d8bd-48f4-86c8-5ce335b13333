
import React from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, Users, Building2 } from "lucide-react";
import { Employee, Section } from "@/types";
import OverviewReport from "./OverviewReport";
import EmployeeReport from "./EmployeeReport";
import SectionReport from "./SectionReport";

interface ReportsLayoutProps {
  employees: Employee[];
  sections: Section[];
  cashierBalance: number;
  currency: (amount: number) => string;
}

const ReportsLayout = ({ employees, sections, cashierBalance, currency }: ReportsLayoutProps) => {
  return (
    <Tabs defaultValue="overview" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="overview" className="flex items-center gap-2">
          <FileText className="w-4 h-4" />
          نظرة عامة
        </TabsTrigger>
        <TabsTrigger value="employees" className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          تقارير الموظفين
          <Badge variant="secondary" className="text-xs">
            {employees.length}
          </Badge>
        </TabsTrigger>
        <TabsTrigger value="sections" className="flex items-center gap-2">
          <Building2 className="w-4 h-4" />
          تقارير الأقسام
          <Badge variant="secondary" className="text-xs">
            {sections.length}
          </Badge>
        </TabsTrigger>
      </TabsList>

      {/* النظرة العامة */}
      <TabsContent value="overview">
        <OverviewReport 
          employees={employees}
          sections={sections}
          cashierBalance={cashierBalance}
          currency={currency}
        />
      </TabsContent>

      {/* تقارير الموظفين */}
      <TabsContent value="employees">
        <div className="space-y-6">
          {employees.length === 0 ? (
            <Card>
              <CardContent className="py-8">
                <div className="text-center text-gray-500">
                  <Users className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>لا يوجد موظفين لعرض تقاريرهم</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            employees.map(employee => (
              <EmployeeReport 
                key={employee.id} 
                employee={employee} 
                currency={currency}
              />
            ))
          )}
        </div>
      </TabsContent>

      {/* تقارير الأقسام */}
      <TabsContent value="sections">
        <div className="space-y-6">
          {sections.length === 0 ? (
            <Card>
              <CardContent className="py-8">
                <div className="text-center text-gray-500">
                  <Building2 className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>لا يوجد أقسام لعرض تقاريرها</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            sections.map(section => (
              <SectionReport 
                key={section.id} 
                section={section} 
                currency={currency}
              />
            ))
          )}
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default ReportsLayout;
