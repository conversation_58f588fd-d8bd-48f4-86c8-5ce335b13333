
import * as React from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Wallet, Factory } from "lucide-react";

type HeaderProps = {
  cashierBalance: number;
  deposit: string;
  onDepositChange: (value: string) => void;
  onDepositSubmit: (e: React.FormEvent) => void;
  sectionsCount: number;
  currency: (amount: number) => string;
};

const Header = ({
  cashierBalance,
  deposit,
  onDepositChange,
  onDepositSubmit,
  sectionsCount,
  currency,
}: HeaderProps) => {
  return (
    <header className="space-y-4">
      <h1 className="text-3xl sm:text-4xl font-bold text-primary tracking-wide">
        نظام تتبع مصروفات مصنع الأثاث
      </h1>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-primary text-primary-foreground">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الخزينة المركزية</CardTitle>
            <Wallet className="h-5 w-5 text-primary-foreground/80" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currency(cashierBalance)}</div>
            <form className="flex gap-2 mt-4" onSubmit={onDepositSubmit}>
              <Input
                className="h-9 bg-primary-foreground/10 text-primary-foreground placeholder:text-primary-foreground/60 border-primary-foreground/40"
                type="number"
                min={1}
                placeholder="مبلغ الإيداع"
                value={deposit}
                onChange={e => onDepositChange(e.target.value)}
              />
              <Button size="sm" type="submit" variant="secondary">إيداع</Button>
            </form>
          </CardContent>
        </Card>
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">عدد الأقسام</CardTitle>
                <Factory className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{sectionsCount}</div>
            </CardContent>
        </Card>
      </div>
    </header>
  );
};

export default Header;
