const { db } = require('../database');

class EmployeeModel {
  // الحصول على جميع الموظفين مع بياناتهم الكاملة
  static getAllEmployees() {
    const employees = db.prepare(`
      SELECT * FROM employees ORDER BY name
    `).all();

    return employees.map(employee => ({
      ...employee,
      withdraws: this.getEmployeeWithdraws(employee.id),
      balanceTransactions: this.getEmployeeBalanceTransactions(employee.id),
      additions: this.getEmployeeAdditions(employee.id),
      salaryDeductions: this.getEmployeeSalaryDeductions(employee.id),
      overdraftRecords: this.getEmployeeOverdraftRecords(employee.id)
    }));
  }

  // الحصول على موظف واحد
  static getEmployeeById(id) {
    const employee = db.prepare(`
      SELECT * FROM employees WHERE id = ?
    `).get(id);

    if (!employee) return null;

    return {
      ...employee,
      withdraws: this.getEmployeeWithdraws(id),
      balanceTransactions: this.getEmployeeBalanceTransactions(id),
      additions: this.getEmployeeAdditions(id),
      salaryDeductions: this.getEmployeeSalaryDeductions(id),
      overdraftRecords: this.getEmployeeOverdraftRecords(id)
    };
  }

  // إضافة موظف جديد
  static createEmployee(name, fixedSalary = 0) {
    const stmt = db.prepare(`
      INSERT INTO employees (name, fixedSalary, balance)
      VALUES (?, ?, ?)
    `);
    
    const result = stmt.run(name, fixedSalary, fixedSalary);
    
    // إضافة معاملة رصيد للمرتب الأساسي
    if (fixedSalary > 0) {
      this.addBalanceTransaction(result.lastInsertRowid, fixedSalary, 'المرتب الأساسي', 'deposit');
    }
    
    return this.getEmployeeById(result.lastInsertRowid);
  }

  // تحديث موظف
  static updateEmployee(id, data) {
    const stmt = db.prepare(`
      UPDATE employees 
      SET name = ?, balance = ?, maxLimit = ?, fixedSalary = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    
    stmt.run(data.name, data.balance, data.maxLimit, data.fixedSalary, id);
    return this.getEmployeeById(id);
  }

  // حذف موظف
  static deleteEmployee(id) {
    const stmt = db.prepare(`DELETE FROM employees WHERE id = ?`);
    return stmt.run(id);
  }

  // الحصول على سحوبات الموظف
  static getEmployeeWithdraws(employeeId) {
    return db.prepare(`
      SELECT * FROM employee_withdraws 
      WHERE employee_id = ? 
      ORDER BY date DESC
    `).all(employeeId).map(withdraw => ({
      id: withdraw.id,
      desc: withdraw.description,
      amount: withdraw.amount,
      date: withdraw.date
    }));
  }

  // إضافة سحبة للموظف
  static addWithdraw(employeeId, description, amount, date) {
    const transaction = db.transaction(() => {
      // إضافة السحبة
      const withdrawStmt = db.prepare(`
        INSERT INTO employee_withdraws (employee_id, description, amount, date)
        VALUES (?, ?, ?, ?)
      `);
      const withdrawResult = withdrawStmt.run(employeeId, description, amount, date);

      // الحصول على رصيد الموظف الحالي
      const employee = db.prepare('SELECT balance FROM employees WHERE id = ?').get(employeeId);
      const availableFromEmployee = Math.max(0, employee.balance);
      const overdraftAmount = Math.max(0, amount - employee.balance);

      // تحديث رصيد الموظف (لا يقل عن صفر)
      const updateBalanceStmt = db.prepare(`
        UPDATE employees SET balance = MAX(0, balance - ?), updated_at = CURRENT_TIMESTAMP WHERE id = ?
      `);
      updateBalanceStmt.run(amount, employeeId);

      // إضافة سجل السحب على المكشوف إذا لزم الأمر
      if (overdraftAmount > 0) {
        const overdraftStmt = db.prepare(`
          INSERT INTO employee_overdraft_records 
          (employee_id, amount, withdraw_id, date, remaining_amount, status)
          VALUES (?, ?, ?, ?, ?, 'pending')
        `);
        overdraftStmt.run(employeeId, overdraftAmount, withdrawResult.lastInsertRowid, new Date().toISOString(), overdraftAmount);
      }

      return {
        id: withdrawResult.lastInsertRowid,
        desc: description,
        amount: amount,
        date: date
      };
    });

    return transaction();
  }

  // الحصول على معاملات الرصيد
  static getEmployeeBalanceTransactions(employeeId) {
    return db.prepare(`
      SELECT * FROM employee_balance_transactions 
      WHERE employee_id = ? 
      ORDER BY date DESC
    `).all(employeeId);
  }

  // إضافة معاملة رصيد
  static addBalanceTransaction(employeeId, amount, notes, type) {
    const stmt = db.prepare(`
      INSERT INTO employee_balance_transactions (employee_id, amount, notes, date, type)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(employeeId, amount, notes, new Date().toISOString(), type);
    
    return {
      id: result.lastInsertRowid,
      amount,
      notes,
      date: new Date().toISOString(),
      type
    };
  }

  // الحصول على إضافات الموظف
  static getEmployeeAdditions(employeeId) {
    return db.prepare(`
      SELECT * FROM employee_additions 
      WHERE employee_id = ? 
      ORDER BY date DESC
    `).all(employeeId);
  }

  // إضافة مبلغ للموظف
  static addEmployeeAddition(employeeId, amount, reason, autoPayOverdraft = false) {
    const transaction = db.transaction(() => {
      // إضافة الإضافة
      const additionStmt = db.prepare(`
        INSERT INTO employee_additions (employee_id, amount, reason, date)
        VALUES (?, ?, ?, ?)
      `);
      const additionResult = additionStmt.run(employeeId, amount, reason, new Date().toISOString());

      // إضافة معاملة رصيد
      this.addBalanceTransaction(employeeId, amount, `إضافة: ${reason}`, 'deposit');

      // حساب السحب على المكشوف المتبقي
      const overdraftRecords = this.getEmployeeOverdraftRecords(employeeId)
        .filter(record => record.status !== 'fully_paid');
      
      const totalOverdraft = overdraftRecords.reduce((sum, record) => sum + record.remainingAmount, 0);
      const amountForOverdraft = autoPayOverdraft ? Math.min(amount, totalOverdraft) : 0;
      const amountForEmployee = amount - amountForOverdraft;

      // تحديث رصيد الموظف
      const updateBalanceStmt = db.prepare(`
        UPDATE employees SET balance = balance + ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?
      `);
      updateBalanceStmt.run(amountForEmployee, employeeId);

      // معالجة السداد التلقائي إذا كان مفعلاً
      if (autoPayOverdraft && amountForOverdraft > 0) {
        this.processOverdraftPayment(employeeId, amountForOverdraft, 'addition', 'سداد تلقائي من الإضافة');
      }

      return {
        id: additionResult.lastInsertRowid,
        amount,
        reason,
        date: new Date().toISOString()
      };
    });

    return transaction();
  }

  // الحصول على خصومات المرتب
  static getEmployeeSalaryDeductions(employeeId) {
    return db.prepare(`
      SELECT * FROM employee_salary_deductions 
      WHERE employee_id = ? 
      ORDER BY date DESC
    `).all(employeeId);
  }

  // إضافة خصم من المرتب
  static addSalaryDeduction(employeeId, amount, reason, month) {
    const stmt = db.prepare(`
      INSERT INTO employee_salary_deductions (employee_id, amount, reason, month, date)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(employeeId, amount, reason, month, new Date().toISOString());
    
    return {
      id: result.lastInsertRowid,
      amount,
      reason,
      month,
      date: new Date().toISOString()
    };
  }

  // الحصول على سجلات السحب على المكشوف
  static getEmployeeOverdraftRecords(employeeId) {
    const records = db.prepare(`
      SELECT * FROM employee_overdraft_records 
      WHERE employee_id = ? 
      ORDER BY date DESC
    `).all(employeeId);

    return records.map(record => ({
      ...record,
      withdrawId: record.withdraw_id,
      remainingAmount: record.remaining_amount,
      payments: this.getOverdraftPayments(record.id)
    }));
  }

  // الحصول على مدفوعات السحب على المكشوف
  static getOverdraftPayments(overdraftRecordId) {
    return db.prepare(`
      SELECT * FROM overdraft_payments 
      WHERE overdraft_record_id = ? 
      ORDER BY date DESC
    `).all(overdraftRecordId);
  }

  // معالجة سداد السحب على المكشوف
  static processOverdraftPayment(employeeId, paymentAmount, source, notes) {
    const overdraftRecords = db.prepare(`
      SELECT * FROM employee_overdraft_records 
      WHERE employee_id = ? AND status != 'fully_paid'
      ORDER BY date ASC
    `).all(employeeId);

    let remainingPayment = paymentAmount;

    for (const record of overdraftRecords) {
      if (remainingPayment <= 0) break;

      const paymentForThisRecord = Math.min(remainingPayment, record.remaining_amount);
      remainingPayment -= paymentForThisRecord;

      // إضافة دفعة
      const paymentStmt = db.prepare(`
        INSERT INTO overdraft_payments (overdraft_record_id, amount, date, source, notes)
        VALUES (?, ?, ?, ?, ?)
      `);
      paymentStmt.run(record.id, paymentForThisRecord, new Date().toISOString(), source, notes);

      // تحديث السجل
      const newRemainingAmount = record.remaining_amount - paymentForThisRecord;
      const newStatus = newRemainingAmount === 0 ? 'fully_paid' : 
                       record.status === 'pending' ? 'partially_paid' : record.status;

      const updateRecordStmt = db.prepare(`
        UPDATE employee_overdraft_records 
        SET remaining_amount = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      updateRecordStmt.run(newRemainingAmount, newStatus, record.id);
    }
  }

  // سداد مبلغ من السحب على المكشوف يدوياً
  static payOverdraftAmount(employeeId, overdraftId, amount) {
    const transaction = db.transaction(() => {
      // التحقق من صحة البيانات
      const overdraftRecord = db.prepare(`
        SELECT * FROM employee_overdraft_records WHERE id = ? AND employee_id = ?
      `).get(overdraftId, employeeId);

      if (!overdraftRecord || amount > overdraftRecord.remaining_amount) {
        throw new Error('Invalid overdraft payment');
      }

      const employee = db.prepare('SELECT balance FROM employees WHERE id = ?').get(employeeId);
      if (amount > employee.balance) {
        throw new Error('Insufficient employee balance');
      }

      // خصم المبلغ من رصيد الموظف
      const updateBalanceStmt = db.prepare(`
        UPDATE employees SET balance = balance - ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?
      `);
      updateBalanceStmt.run(amount, employeeId);

      // إضافة دفعة
      const paymentStmt = db.prepare(`
        INSERT INTO overdraft_payments (overdraft_record_id, amount, date, source, notes)
        VALUES (?, ?, ?, 'manual', 'سداد يدوي')
      `);
      paymentStmt.run(overdraftId, amount, new Date().toISOString());

      // تحديث السجل
      const newRemainingAmount = overdraftRecord.remaining_amount - amount;
      const newStatus = newRemainingAmount === 0 ? 'fully_paid' : 
                       overdraftRecord.status === 'pending' ? 'partially_paid' : overdraftRecord.status;

      const updateRecordStmt = db.prepare(`
        UPDATE employee_overdraft_records 
        SET remaining_amount = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      updateRecordStmt.run(newRemainingAmount, newStatus, overdraftId);

      return { success: true };
    });

    return transaction();
  }
}

module.exports = EmployeeModel;
