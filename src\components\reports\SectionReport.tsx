
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Printer, Building2, TrendingDown } from "lucide-react";
import { Section } from "@/types";

interface SectionReportProps {
  section: Section;
  currency: (amount: number) => string;
}

const SectionReport = ({ section, currency }: SectionReportProps) => {
  const handlePrint = () => {
    const printContent = document.getElementById(`section-report-${section.id}`);
    if (printContent) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>تقرير القسم - ${section.name}</title>
              <style>
                body { font-family: <PERSON><PERSON>, sans-serif; direction: rtl; margin: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; }
                .header { text-align: center; margin-bottom: 30px; }
                .summary { margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 8px; }
                .print-date { text-align: left; margin-bottom: 20px; font-size: 12px; color: #666; }
              </style>
            </head>
            <body>
              <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString("ar-LY")}</div>
              ${printContent.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  const totalExpenses = section.expenses.reduce((sum, exp) => sum + exp.amount, 0);
  const sortedExpenses = [...section.expenses].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <Building2 className="w-5 h-5" />
          <CardTitle>تقرير القسم: {section.name}</CardTitle>
        </div>
        <Button onClick={handlePrint} variant="outline" size="sm">
          <Printer className="w-4 h-4 mr-2" />
          طباعة
        </Button>
      </CardHeader>
      <CardContent>
        <div id={`section-report-${section.id}`}>
          <div className="header">
            <h2 className="text-2xl font-bold">تقرير مصروفات مفصل</h2>
            <h3 className="text-xl">القسم: {section.name}</h3>
          </div>

          {/* ملخص المصروفات */}
          <div className="summary bg-red-50 p-6 rounded-lg mb-6">
            <div className="flex items-center gap-4">
              <TrendingDown className="w-8 h-8 text-red-600" />
              <div>
                <p className="text-lg font-semibold text-red-800">إجمالي المصروفات</p>
                <p className="text-2xl font-bold text-red-600">{currency(totalExpenses)}</p>
              </div>
              <div className="mr-auto">
                <p className="text-sm text-gray-600">عدد المصروفات</p>
                <p className="text-lg font-semibold">{section.expenses.length}</p>
              </div>
            </div>
          </div>

          {/* تفاصيل المصروفات */}
          <div className="overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الوصف</TableHead>
                  <TableHead>المبلغ</TableHead>
                  <TableHead>رقم المصروف</TableHead>
                  <TableHead>التاريخ</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedExpenses.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center text-gray-500 py-8">
                      لا توجد مصروفات لهذا القسم
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedExpenses.map((expense) => (
                    <TableRow key={expense.id}>
                      <TableCell className="font-medium">{expense.desc}</TableCell>
                      <TableCell className="font-mono text-red-600">
                        {currency(expense.amount)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        S-{section.id}-{expense.id}
                      </TableCell>
                      <TableCell>
                        {new Date(expense.date).toLocaleDateString("ar-LY")}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SectionReport;
