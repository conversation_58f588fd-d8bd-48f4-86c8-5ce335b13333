const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const {
  readData,
  writeData,
  readDataWithDefaults,
  writeDataSafe,
  initializeStorage,
  createFullBackup,
  validateDataIntegrity
} = require('./storage');

const isDev = process.env.NODE_ENV === 'development';
let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '..', 'public', 'favicon.ico'),
    title: 'نظام إدارة مصنع الأثاث المالي',
    show: false
  });

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:8080');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '..', 'dist', 'index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.whenReady().then(() => {
  // تهيئة نظام التخزين المحسن
  const integrity = initializeStorage();
  console.log('Storage integrity check:', integrity);

  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// معالجات IPC للتواصل مع الواجهة الأمامية
// الخزينة
ipcMain.handle('get-cashier-balance', () => {
  const cashierData = readData('cashier');
  return cashierData ? cashierData.balance : 0;
});

ipcMain.handle('update-cashier-balance', (event, balance) => {
  const cashierData = { balance, updated_at: new Date().toISOString() };
  return writeDataSafe('cashier', cashierData);
});

// الأقسام
ipcMain.handle('get-sections', () => {
  const sectionsData = readData('sections');
  return sectionsData || [];
});

ipcMain.handle('add-section', (event, name) => {
  const sectionsData = readData('sections') || [];
  const newSection = {
    id: sectionsData.length === 0 ? 1 : Math.max(...sectionsData.map(s => s.id)) + 1,
    name,
    expenses: [],
    created_at: new Date().toISOString()
  };
  sectionsData.push(newSection);
  writeDataSafe('sections', sectionsData);
  return newSection;
});

ipcMain.handle('delete-section', (event, id) => {
  const sectionsData = readData('sections') || [];
  const filteredSections = sectionsData.filter(section => section.id !== id);
  return writeDataSafe('sections', filteredSections);
});

ipcMain.handle('add-expense', (event, { sectionId, description, amount, date }) => {
  const sectionsData = readData('sections') || [];
  const sectionIndex = sectionsData.findIndex(s => s.id === sectionId);

  if (sectionIndex !== -1) {
    const newExpense = {
      id: sectionsData[sectionIndex].expenses.length === 0 ? 1 :
          Math.max(...sectionsData[sectionIndex].expenses.map(e => e.id)) + 1,
      desc: description,
      amount,
      date
    };
    sectionsData[sectionIndex].expenses.push(newExpense);
    writeData('sections', sectionsData);
    return newExpense;
  }
  return null;
});

// الموظفين
ipcMain.handle('get-employees', () => {
  const employeesData = readData('employees');
  return employeesData || [];
});

ipcMain.handle('add-employee', (event, { name, balance, maxLimit, fixedSalary }) => {
  const employeesData = readData('employees') || [];
  const newEmployee = {
    id: employeesData.length === 0 ? 1 : Math.max(...employeesData.map(e => e.id)) + 1,
    name,
    balance: fixedSalary || 0, // الرصيد الابتدائي = المرتب الأساسي
    maxLimit: 0, // لم يعد مستخدماً
    fixedSalary: fixedSalary || 0,
    withdraws: [],
    balanceTransactions: fixedSalary > 0 ? [{
      id: 1,
      amount: fixedSalary,
      notes: 'المرتب الأساسي',
      date: new Date().toISOString(),
      type: 'deposit'
    }] : [],
    salaryDeductions: [],
    additions: [], // إضافة حقل الإضافات الجديد
    created_at: new Date().toISOString()
  };

  employeesData.push(newEmployee);
  writeData('employees', employeesData);
  return newEmployee;
});

ipcMain.handle('remove-employee', (event, id) => {
  const employeesData = readData('employees') || [];
  const filteredEmployees = employeesData.filter(employee => employee.id !== id);
  return writeData('employees', filteredEmployees);
});

ipcMain.handle('update-employee-balance', (event, { id, amount, notes }) => {
  const employeesData = readData('employees') || [];
  const employeeIndex = employeesData.findIndex(e => e.id === id);

  if (employeeIndex !== -1) {
    employeesData[employeeIndex].balance += amount;

    const newTransaction = {
      id: employeesData[employeeIndex].balanceTransactions.length === 0 ? 1 :
          Math.max(...employeesData[employeeIndex].balanceTransactions.map(t => t.id)) + 1,
      amount,
      notes: notes || '',
      date: new Date().toISOString(),
      type: amount > 0 ? 'deposit' : 'withdraw'
    };

    employeesData[employeeIndex].balanceTransactions.push(newTransaction);
    writeData('employees', employeesData);
    return newTransaction;
  }
  return null;
});

ipcMain.handle('add-withdraw', (event, { employeeId, description, amount, date }) => {
  const employeesData = readData('employees') || [];
  const employeeIndex = employeesData.findIndex(e => e.id === employeeId);

  if (employeeIndex !== -1) {
    const employee = employeesData[employeeIndex];

    // التأكد من وجود حقل السحب على المكشوف
    if (!employee.overdraftRecords) {
      employee.overdraftRecords = [];
    }

    const newWithdraw = {
      id: employee.withdraws.length === 0 ? 1 :
          Math.max(...employee.withdraws.map(w => w.id)) + 1,
      desc: description,
      amount,
      date
    };

    employee.withdraws.push(newWithdraw);

    // حساب المبلغ المتاح من رصيد الموظف
    const availableFromEmployee = Math.max(0, employee.balance);
    const overdraftAmount = Math.max(0, amount - employee.balance);

    // تحديث رصيد الموظف (لا يقل عن صفر)
    employee.balance = Math.max(0, employee.balance - amount);

    // إضافة سجل السحب على المكشوف إذا لزم الأمر
    if (overdraftAmount > 0) {
      const newOverdraftRecord = {
        id: employee.overdraftRecords.length === 0 ? 1 :
            Math.max(...employee.overdraftRecords.map(o => o.id)) + 1,
        amount: overdraftAmount,
        withdrawId: newWithdraw.id,
        date: new Date().toISOString(),
        remainingAmount: overdraftAmount,
        status: 'pending',
        payments: []
      };

      employee.overdraftRecords.push(newOverdraftRecord);
    }

    writeData('employees', employeesData);
    return newWithdraw;
  }
  return null;
});

// إضافة مبلغ للموظف (علاوة، مكافأة، إلخ)
ipcMain.handle('add-employee-addition', (event, { employeeId, amount, reason, autoPayOverdraft = false }) => {
  const employeesData = readData('employees') || [];
  const employeeIndex = employeesData.findIndex(e => e.id === employeeId);

  if (employeeIndex !== -1) {
    const employee = employeesData[employeeIndex];

    // التأكد من وجود الحقول المطلوبة
    if (!employee.additions) {
      employee.additions = [];
    }
    if (!employee.balanceTransactions) {
      employee.balanceTransactions = [];
    }
    if (!employee.overdraftRecords) {
      employee.overdraftRecords = [];
    }

    // حساب السحب على المكشوف المتبقي
    const totalOverdraft = employee.overdraftRecords
      .filter(record => record.status !== 'fully_paid')
      .reduce((total, record) => total + record.remainingAmount, 0);

    const amountForOverdraft = autoPayOverdraft ? Math.min(amount, totalOverdraft) : 0;
    const amountForEmployee = amount - amountForOverdraft;

    const newAddition = {
      id: employee.additions.length === 0 ? 1 : Math.max(...employee.additions.map(a => a.id)) + 1,
      amount,
      reason,
      date: new Date().toISOString()
    };

    employee.additions.push(newAddition);
    employee.balance += amountForEmployee;

    // إضافة معاملة رصيد للتتبع
    const newTransaction = {
      id: employee.balanceTransactions.length === 0 ? 1 : Math.max(...employee.balanceTransactions.map(t => t.id)) + 1,
      amount,
      notes: `إضافة: ${reason}`,
      date: new Date().toISOString(),
      type: 'deposit'
    };

    employee.balanceTransactions.push(newTransaction);

    // معالجة السداد التلقائي إذا كان مفعلاً
    if (autoPayOverdraft && amountForOverdraft > 0) {
      let remainingPayment = amountForOverdraft;

      employee.overdraftRecords = employee.overdraftRecords.map(record => {
        if (remainingPayment <= 0 || record.status === 'fully_paid') {
          return record;
        }

        const paymentForThisRecord = Math.min(remainingPayment, record.remainingAmount);
        remainingPayment -= paymentForThisRecord;

        const newPayment = {
          id: record.payments.length === 0 ? 1 : Math.max(...record.payments.map(p => p.id)) + 1,
          amount: paymentForThisRecord,
          date: new Date().toISOString(),
          source: 'addition',
          notes: 'سداد تلقائي من الإضافة'
        };

        const newRemainingAmount = record.remainingAmount - paymentForThisRecord;
        const newStatus = newRemainingAmount === 0 ? 'fully_paid' :
                         record.payments.length === 0 ? 'partially_paid' : record.status;

        return {
          ...record,
          remainingAmount: newRemainingAmount,
          status: newStatus,
          payments: [...record.payments, newPayment]
        };
      });
    }

    writeData('employees', employeesData);
    return newAddition;
  }
  return null;
});

// سداد السحب على المكشوف
ipcMain.handle('pay-overdraft-amount', (event, { employeeId, overdraftId, amount }) => {
  const employeesData = readData('employees') || [];
  const employeeIndex = employeesData.findIndex(e => e.id === employeeId);

  if (employeeIndex !== -1) {
    const employee = employeesData[employeeIndex];

    if (!employee.overdraftRecords) {
      employee.overdraftRecords = [];
    }

    const overdraftRecord = employee.overdraftRecords.find(r => r.id === overdraftId);
    if (!overdraftRecord) {
      return null;
    }

    if (amount <= 0 || amount > overdraftRecord.remainingAmount || amount > employee.balance) {
      return null;
    }

    // خصم المبلغ من رصيد الموظف
    employee.balance -= amount;

    // تحديث سجل السحب على المكشوف
    const recordIndex = employee.overdraftRecords.findIndex(r => r.id === overdraftId);
    if (recordIndex !== -1) {
      const record = employee.overdraftRecords[recordIndex];

      const newPayment = {
        id: record.payments.length === 0 ? 1 : Math.max(...record.payments.map(p => p.id)) + 1,
        amount,
        date: new Date().toISOString(),
        source: 'manual',
        notes: 'سداد يدوي'
      };

      const newRemainingAmount = record.remainingAmount - amount;
      const newStatus = newRemainingAmount === 0 ? 'fully_paid' :
                       record.payments.length === 0 ? 'partially_paid' : record.status;

      employee.overdraftRecords[recordIndex] = {
        ...record,
        remainingAmount: newRemainingAmount,
        status: newStatus,
        payments: [...record.payments, newPayment]
      };
    }

    writeData('employees', employeesData);
    return { success: true };
  }
  return null;
});

// الخزينة المتقدمة
ipcMain.handle('get-cashier-deposits', () => {
  const depositsData = readData('cashier-deposits');
  return depositsData || [];
});

ipcMain.handle('add-advanced-deposit', (event, { amount, invoiceNumber, customerId, customerName, remainingAmount, paidAmount, status }) => {
  const depositsData = readData('cashier-deposits') || [];
  const newDeposit = {
    id: depositsData.length === 0 ? 1 : Math.max(...depositsData.map(d => d.id)) + 1,
    amount,
    invoiceNumber,
    customerId,
    customerName,
    remainingAmount,
    paidAmount,
    status: status || (remainingAmount > 0 ? 'pending' : 'completed'),
    date: new Date().toISOString()
  };

  depositsData.push(newDeposit);
  writeData('cashier-deposits', depositsData);

  // تحديث رصيد الخزينة (إضافة المبلغ المدفوع فقط)
  const cashierData = readData('cashier') || { balance: 0 };
  cashierData.balance += paidAmount;
  writeData('cashier', cashierData);

  return newDeposit;
});

// العملاء
ipcMain.handle('get-customers', () => {
  const customersData = readData('customers');
  console.log('Loading customers from file:', customersData ? customersData.length : 0, 'customers');
  return customersData || [];
});

ipcMain.handle('add-customer', (event, { name, phone, address }) => {
  const customersData = readData('customers') || [];
  const newCustomer = {
    id: customersData.length === 0 ? 1 : Math.max(...customersData.map(c => c.id)) + 1,
    name,
    phone: phone || '',
    address: address || '',
    totalInvoices: 0,
    totalPaid: 0,
    totalRemaining: 0,
    invoices: [],
    payments: []
  };

  customersData.push(newCustomer);
  writeData('customers', customersData);
  return newCustomer;
});

ipcMain.handle('update-customer', (event, id, customerData) => {
  const customersData = readData('customers') || [];
  const customerIndex = customersData.findIndex(c => c.id === id);

  if (customerIndex !== -1) {
    customersData[customerIndex] = { ...customersData[customerIndex], ...customerData };
    writeData('customers', customersData);
    return customersData[customerIndex];
  }
  return null;
});

ipcMain.handle('remove-customer', (event, id) => {
  const customersData = readData('customers') || [];
  const filteredCustomers = customersData.filter(customer => customer.id !== id);
  return writeData('customers', filteredCustomers);
});

// حفظ جميع العملاء (للتحديث الشامل)
ipcMain.handle('save-customers', (event, customersData) => {
  console.log('Saving customers to file:', customersData.length, 'customers');
  const result = writeData('customers', customersData);
  console.log('Save result:', result);
  return result;
});

// المدفوعات
ipcMain.handle('get-payments', () => {
  const paymentsData = readData('payments');
  return paymentsData || [];
});

ipcMain.handle('add-payment', (event, paymentData) => {
  const paymentsData = readData('payments') || [];
  const newPayment = {
    id: paymentsData.length === 0 ? 1 : Math.max(...paymentsData.map(p => p.id)) + 1,
    ...paymentData,
    paymentDate: new Date().toISOString()
  };

  paymentsData.push(newPayment);
  writeData('payments', paymentsData);

  // إضافة المبلغ لرصيد الخزينة
  const cashierData = readData('cashier') || { balance: 0 };
  cashierData.balance += paymentData.amount;
  writeData('cashier', cashierData);

  return newPayment;
});

ipcMain.handle('save-payments', (event, paymentsData) => {
  console.log('Saving payments to file:', paymentsData.length, 'payments');
  const result = writeDataSafe('payments', paymentsData);
  console.log('Save result:', result);
  return result;
});

// معالجات إدارة البيانات والنسخ الاحتياطية
ipcMain.handle('create-backup', () => {
  return createFullBackup();
});

ipcMain.handle('validate-data', () => {
  return validateDataIntegrity();
});

ipcMain.handle('get-storage-info', () => {
  const employees = readData('employees') || [];
  const customers = readData('customers') || [];
  const sections = readData('sections') || [];
  const cashier = readData('cashier') || { balance: 0 };

  return {
    employees: employees.length,
    customers: customers.length,
    sections: sections.length,
    cashierBalance: cashier.balance,
    lastBackup: new Date().toISOString()
  };
});
