export interface Expense {
  id: number;
  desc: string;
  amount: number;
  date: string; // ISO date string
}

export interface Section {
  id: number;
  name: string;
  expenses: Expense[];
}

export interface Withdraw {
  id: number;
  desc: string;
  amount: number;
  date: string; // ISO date string
}

export interface BalanceTransaction {
  id: number;
  amount: number;
  notes: string;
  date: string; // ISO date string
  type: 'deposit' | 'deduction'; // إيداع أو خصم
}

export interface EmployeeAddition {
  id: number;
  amount: number;
  reason: string; // سبب الإضافة (علاوة، مكافأة، إلخ)
  date: string; // ISO date string
  addedBy?: string; // من أضاف المبلغ (اختياري)
}

// سجل السحب على المكشوف
export interface OverdraftRecord {
  id: number;
  amount: number; // المبلغ المسحوب على المكشوف
  withdrawId: number; // رقم السحبة المرتبطة
  date: string; // تاريخ السحب على المكشوف
  remainingAmount: number; // المبلغ المتبقي للسداد
  status: 'pending' | 'partially_paid' | 'fully_paid'; // حالة السداد
  payments: OverdraftPayment[]; // سجل المدفوعات
}

// دفعة سداد السحب على المكشوف
export interface OverdraftPayment {
  id: number;
  amount: number; // مبلغ الدفعة
  date: string; // تاريخ الدفعة
  source: 'addition' | 'manual'; // مصدر الدفعة (إضافة تلقائية أو يدوية)
  notes?: string; // ملاحظات
}

// نوع معاملة شامل لكشف الحساب
export interface AccountTransaction {
  id: number;
  type: 'salary' | 'addition' | 'withdrawal' | 'adjustment'; // نوع المعاملة
  amount: number; // المبلغ (موجب للإضافات، سالب للسحوبات)
  description: string; // وصف المعاملة
  date: string; // ISO date string
  balanceAfter: number; // الرصيد بعد المعاملة
  reference?: string; // مرجع المعاملة (رقم الفاتورة، رقم السحب، إلخ)
  category?: string; // فئة المعاملة (راتب، علاوة، مكافأة، سحب، إلخ)
}

export interface SalaryDeduction {
  id: number;
  amount: number;
  reason: string;
  month: string; // YYYY-MM format
  date: string; // ISO date string
}

export interface CashierDeposit {
  id: number;
  amount: number;
  invoiceNumber: string; // رقم الفاتورة
  customerId: number; // معرف العميل
  customerName: string; // اسم العميل (للعرض)
  remainingAmount: number; // المبلغ المتبقي
  paidAmount: number; // المبلغ المدفوع
  date: string; // ISO date string
  status: 'completed' | 'pending'; // حالة الفاتورة
}

export interface Payment {
  id: number;
  invoiceId: number;
  customerId: number;
  amount: number;
  paymentDate: string;
  notes?: string;
  type: 'partial' | 'full'; // نوع الدفعة
}

export interface Customer {
  id: number;
  name: string;
  phone?: string;
  address?: string;
  totalInvoices: number;
  totalPaid: number;
  totalRemaining: number;
  invoices: CashierDeposit[];
  payments: Payment[]; // سجل المدفوعات
}

export interface Employee {
  id: number;
  name: string;
  balance: number; // رصيد الموظف
  maxLimit: number; // الحد الأقصى للسحب (لن يُستخدم بعد الآن)
  fixedSalary: number; // المرتب الثابت
  withdraws: Withdraw[];
  balanceTransactions: BalanceTransaction[]; // تاريخ تعديلات الرصيد
  salaryDeductions: SalaryDeduction[]; // خصومات المرتب
  additions: EmployeeAddition[]; // الإضافات المالية (علاوات، مكافآت، إلخ)
  overdraftRecords: OverdraftRecord[]; // سجل السحب على المكشوف
}

export interface AppContextType {
  sections: Section[];
  employees: Employee[];
  customers: Customer[];
  cashierBalance: number;
  cashierDeposits: CashierDeposit[];
  deposit: string;
  setDeposit: (value: string) => void;
  handleDeposit: (e: React.FormEvent) => void;
  handleAdvancedDeposit: (depositData: Omit<CashierDeposit, 'id' | 'date' | 'status'>) => void;
  addPaymentToCashier: (amount: number) => void;
  createInvoiceForCustomer: (customerId: number, invoiceData: Omit<CashierDeposit, 'id' | 'date' | 'customerId' | 'customerName' | 'status'>) => void;
  newSectionName: string;
  setNewSectionName: (value: string) => void;
  handleAddSection: (e: React.FormEvent) => void;
  handleDeleteSection: (id: number) => void;
  expenseInputs: { [id: number]: { desc: string; amount: string; date: Date | null } };
  handleExpenseInputChange: (
    sectionId: number,
    field: "desc" | "amount" | "date",
    value: string | Date | null
  ) => void;
  handleAddExpense: (sectionId: number, e: React.FormEvent) => void;
  newWithdraws: { [id: number]: { desc: string; amount: string; date: Date | null } };
  handleNewWithdrawChange: (
    empId: number,
    field: "desc" | "amount" | "date",
    value: string | Date | null
  ) => void;
  handleAddWithdraw: (empId: number, e: React.FormEvent) => void;
  currency: (amount: number) => string;
  addEmployee: (name: string, balance: number, maxLimit: number, fixedSalary: number) => void;
  removeEmployee: (id: number) => void;
  updateEmployeeBalance: (id: number, amount: number, notes?: string) => void;
  getEmployeeMonthlyWithdraws: (empId: number, year: number, month: number) => Withdraw[];
  getEmployeeTotalDeposits: (empId: number) => number;
  getEmployeeTotalWithdraws: (empId: number) => number;
  getEmployeeTotalAdditions: (empId: number) => number;
  getEmployeeAvailableBalance: (empId: number) => number;
  getEmployeeTotalOverdraft: (empId: number) => number;
  addEmployeeAddition: (empId: number, amount: number, reason: string, autoPayOverdraft?: boolean) => void;
  payOverdraftAmount: (empId: number, overdraftId: number, amount: number) => void;
  addSalaryDeduction: (empId: number, amount: number, reason: string, month: string) => void;
  // إدارة العملاء
  addCustomer: (customer: Omit<Customer, 'id' | 'totalInvoices' | 'totalPaid' | 'totalRemaining' | 'invoices'>) => void;
  updateCustomer: (id: number, customer: Partial<Customer>) => void;
  removeCustomer: (id: number) => void;
  getCustomerInvoices: (customerId: number) => CashierDeposit[];
  makePayment: (customerId: number, invoiceId: number, amount: number, type: 'partial' | 'full', notes?: string) => void;
  getCustomerPayments: (customerId: number) => Payment[];
  updateInvoiceStatus: (invoiceId: number) => void;
  updateCustomersWithExistingInvoices: (existingInvoices: CashierDeposit[]) => void;
}
