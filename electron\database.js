const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { app } = require('electron');

// إنشاء مجلد البيانات إذا لم يكن موجوداً
const userDataPath = app.getPath('userData');
const dbPath = path.join(userDataPath, 'furniture_factory.db');

// إنشاء اتصال قاعدة البيانات
const db = new sqlite3.Database(dbPath);

// تفعيل الـ foreign keys
db.run('PRAGMA foreign_keys = ON');

// إنشاء الجداول
function initializeDatabase() {
  return new Promise((resolve, reject) => {
    // جدول الموظفين
    db.run(`
    CREATE TABLE IF NOT EXISTS employees (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      balance REAL DEFAULT 0,
      maxLimit REAL DEFAULT 0,
      fixedSalary REAL DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول سحوبات الموظفين
  db.exec(`
    CREATE TABLE IF NOT EXISTS employee_withdraws (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_id INTEGER NOT NULL,
      description TEXT NOT NULL,
      amount REAL NOT NULL,
      date DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
    )
  `);

  // جدول معاملات رصيد الموظفين
  db.exec(`
    CREATE TABLE IF NOT EXISTS employee_balance_transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      notes TEXT,
      date DATETIME NOT NULL,
      type TEXT CHECK(type IN ('deposit', 'deduction')) NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
    )
  `);

  // جدول إضافات الموظفين (علاوات، مكافآت)
  db.exec(`
    CREATE TABLE IF NOT EXISTS employee_additions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      reason TEXT NOT NULL,
      date DATETIME NOT NULL,
      addedBy TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
    )
  `);

  // جدول خصومات المرتب
  db.exec(`
    CREATE TABLE IF NOT EXISTS employee_salary_deductions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      reason TEXT NOT NULL,
      month TEXT NOT NULL,
      date DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
    )
  `);

  // جدول السحب على المكشوف
  db.exec(`
    CREATE TABLE IF NOT EXISTS employee_overdraft_records (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      employee_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      withdraw_id INTEGER NOT NULL,
      date DATETIME NOT NULL,
      remaining_amount REAL NOT NULL,
      status TEXT CHECK(status IN ('pending', 'partially_paid', 'fully_paid')) DEFAULT 'pending',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE,
      FOREIGN KEY (withdraw_id) REFERENCES employee_withdraws (id) ON DELETE CASCADE
    )
  `);

  // جدول مدفوعات السحب على المكشوف
  db.exec(`
    CREATE TABLE IF NOT EXISTS overdraft_payments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      overdraft_record_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      date DATETIME NOT NULL,
      source TEXT CHECK(source IN ('addition', 'manual')) NOT NULL,
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (overdraft_record_id) REFERENCES employee_overdraft_records (id) ON DELETE CASCADE
    )
  `);

  // جدول العملاء
  db.exec(`
    CREATE TABLE IF NOT EXISTS customers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      phone TEXT,
      address TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول مدفوعات العملاء
  db.exec(`
    CREATE TABLE IF NOT EXISTS customer_payments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      customer_id INTEGER NOT NULL,
      amount REAL NOT NULL,
      type TEXT CHECK(type IN ('full', 'partial')) NOT NULL,
      payment_date DATETIME NOT NULL,
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
    )
  `);

  // جدول الخزينة (الإيداعات)
  db.exec(`
    CREATE TABLE IF NOT EXISTS cashier_deposits (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      invoice_number TEXT NOT NULL,
      customer_name TEXT NOT NULL,
      amount REAL NOT NULL,
      paid_amount REAL DEFAULT 0,
      remaining_amount REAL NOT NULL,
      status TEXT CHECK(status IN ('completed', 'pending')) DEFAULT 'pending',
      date DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول الأقسام
  db.exec(`
    CREATE TABLE IF NOT EXISTS sections (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // جدول مصروفات الأقسام
  db.exec(`
    CREATE TABLE IF NOT EXISTS section_expenses (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      section_id INTEGER NOT NULL,
      description TEXT NOT NULL,
      amount REAL NOT NULL,
      date DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE CASCADE
    )
  `);

  // جدول إعدادات النظام
  db.exec(`
    CREATE TABLE IF NOT EXISTS system_settings (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  console.log('Database initialized successfully');
}

// إغلاق قاعدة البيانات عند إغلاق التطبيق
process.on('exit', () => db.close());
process.on('SIGHUP', () => process.exit(128 + 1));
process.on('SIGINT', () => process.exit(128 + 2));
process.on('SIGTERM', () => process.exit(128 + 15));

module.exports = {
  db,
  initializeDatabase
};
