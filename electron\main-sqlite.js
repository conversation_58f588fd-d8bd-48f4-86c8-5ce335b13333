const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const { initializeDatabase } = require('./database');
const EmployeeModel = require('./models/employees');
const CustomerModel = require('./models/customers');
const CashierModel = require('./models/cashier');
const SectionModel = require('./models/sections');
const { migrateData } = require('./migrate-data');

const isDev = process.env.NODE_ENV === 'development';
let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '..', 'public', 'favicon.ico'),
    title: 'نظام إدارة مصنع الأثاث المالي',
    show: false
  });

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:8080');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '..', 'dist', 'index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.whenReady().then(() => {
  // تهيئة قاعدة البيانات
  initializeDatabase();

  // التحقق من وجود بيانات قديمة وترحيلها
  const oldDataPath = path.join(app.getPath('userData'), 'data');
  if (fs.existsSync(oldDataPath)) {
    try {
      console.log('Found old data, starting migration...');
      migrateData();
      console.log('Migration completed successfully');
    } catch (error) {
      console.error('Migration failed:', error);
      // يمكن المتابعة حتى لو فشل الترحيل
    }
  }

  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// معالجات IPC للخزينة
ipcMain.handle('get-cashier-balance', () => {
  return CashierModel.getCashierBalance();
});

ipcMain.handle('update-cashier-balance', (event, balance) => {
  return CashierModel.updateCashierBalance(balance);
});

ipcMain.handle('get-cashier-deposits', () => {
  return CashierModel.getAllDeposits();
});

ipcMain.handle('add-advanced-deposit', (event, { amount, invoiceNumber, customerName }) => {
  return CashierModel.addDeposit(invoiceNumber, customerName, amount, new Date().toISOString());
});

// معالجات IPC للموظفين
ipcMain.handle('get-employees', () => {
  return EmployeeModel.getAllEmployees();
});

ipcMain.handle('add-employee', (event, { name, fixedSalary }) => {
  return EmployeeModel.createEmployee(name, fixedSalary || 0);
});

ipcMain.handle('remove-employee', (event, id) => {
  return EmployeeModel.deleteEmployee(id);
});

ipcMain.handle('update-employee-balance', (event, { id, amount, notes }) => {
  const type = amount > 0 ? 'deposit' : 'deduction';
  const employee = EmployeeModel.getEmployeeById(id);
  if (employee) {
    // تحديث رصيد الموظف
    const newBalance = employee.balance + amount;
    EmployeeModel.updateEmployee(id, { ...employee, balance: newBalance });
    
    // إضافة معاملة رصيد
    return EmployeeModel.addBalanceTransaction(id, amount, notes || '', type);
  }
  return null;
});

ipcMain.handle('add-withdraw', (event, { employeeId, description, amount, date }) => {
  return EmployeeModel.addWithdraw(employeeId, description, amount, date);
});

ipcMain.handle('add-employee-addition', (event, { employeeId, amount, reason, autoPayOverdraft = false }) => {
  return EmployeeModel.addEmployeeAddition(employeeId, amount, reason, autoPayOverdraft);
});

ipcMain.handle('pay-overdraft-amount', (event, { employeeId, overdraftId, amount }) => {
  return EmployeeModel.payOverdraftAmount(employeeId, overdraftId, amount);
});

// معالجات IPC للعملاء
ipcMain.handle('get-customers', () => {
  return CustomerModel.getAllCustomers();
});

ipcMain.handle('add-customer', (event, { name, phone, address }) => {
  return CustomerModel.createCustomer(name, phone, address);
});

ipcMain.handle('update-customer', (event, id, customerData) => {
  return CustomerModel.updateCustomer(id, customerData);
});

ipcMain.handle('remove-customer', (event, id) => {
  return CustomerModel.deleteCustomer(id);
});

ipcMain.handle('save-customers', (event, customersData) => {
  // هذه الوظيفة لن تعد مطلوبة مع SQLite لأن البيانات تُحفظ تلقائياً
  return true;
});

// معالجات IPC للأقسام
ipcMain.handle('get-sections', () => {
  return SectionModel.getAllSections();
});

ipcMain.handle('add-section', (event, name) => {
  return SectionModel.createSection(name);
});

ipcMain.handle('delete-section', (event, id) => {
  return SectionModel.deleteSection(id);
});

ipcMain.handle('add-expense', (event, { sectionId, description, amount, date }) => {
  return SectionModel.addSectionExpense(sectionId, description, amount, date);
});

// معالجات IPC للمدفوعات (مؤقتة - ستحتاج لتطوير أكثر)
ipcMain.handle('get-payments', () => {
  // سيتم تطوير هذا لاحقاً
  return [];
});

ipcMain.handle('add-payment', (event, paymentData) => {
  // سيتم تطوير هذا لاحقاً
  return null;
});

ipcMain.handle('save-payments', (event, paymentsData) => {
  // سيتم تطوير هذا لاحقاً
  return true;
});
