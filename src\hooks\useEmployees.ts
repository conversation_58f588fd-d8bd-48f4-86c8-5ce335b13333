
import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { Employee } from "@/types";
import { useElectronAPI } from "./useElectronAPI";

const currency = (a: number) => a.toLocaleString("ar-LY") + " د.ل";

export const useEmployees = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [newWithdraws, setNewWithdraws] = useState<{ [id: number]: { desc: string; amount: string; date: Date | null } }>({});
  const { isElectron, api } = useElectronAPI();

  // تحميل الموظفين عند بدء التطبيق
  useEffect(() => {
    const loadEmployees = async () => {
      if (isElectron && api) {
        try {
          const loadedEmployees = await api.getEmployees();
          // ترقية البيانات لإضافة الحقول الجديدة إذا لم تكن موجودة
          const upgradedEmployees = loadedEmployees.map(emp => ({
            ...emp,
            additions: emp.additions || [],
            overdraftRecords: emp.overdraftRecords || []
          }));
          setEmployees(upgradedEmployees);
        } catch (error) {
          console.error('Error loading employees:', error);
        }
      } else {
        // استخدام localStorage كبديل للمتصفح
        const savedEmployees = localStorage.getItem('employees');
        if (savedEmployees) {
          const parsedEmployees = JSON.parse(savedEmployees);
          // ترقية البيانات لإضافة الحقول الجديدة إذا لم تكن موجودة
          const upgradedEmployees = parsedEmployees.map((emp: Employee) => ({
            ...emp,
            additions: emp.additions || [],
            overdraftRecords: emp.overdraftRecords || []
          }));
          setEmployees(upgradedEmployees);
        }
      }
    };

    loadEmployees();
  }, [isElectron, api]);

  // حفظ الموظفين
  const saveEmployees = (newEmployees: Employee[]) => {
    if (!isElectron) {
      localStorage.setItem('employees', JSON.stringify(newEmployees));
    }
  };

  const handleNewWithdrawChange = (empId: number, field: "desc" | "amount" | "date", value: string | Date | null) => {
    setNewWithdraws((inputs) => ({
      ...inputs,
      [empId]: { ...(inputs[empId] || { desc: "", amount: "", date: null }), [field]: value },
    }));
  };

  const handleAddWithdraw = async (empId: number, e: React.FormEvent, cashierBalance: number, deductFromCashier: (amount: number) => void) => {
    e.preventDefault();
    const curr = newWithdraws[empId] || { desc: "", amount: "", date: null };
    const val = Number(curr.amount);

    if (!curr.desc.trim() || val <= 0 || !curr.date) {
      toast({ title: "تحذير", description: "يرجى إدخال وصف ومبلغ وتاريخ السحبة.", variant: "destructive" });
      return;
    }

    const employee = employees.find(emp => emp.id === empId);
    if (!employee) {
      toast({ title: "خطأ", description: "الموظف غير موجود.", variant: "destructive" });
      return;
    }

    // حساب المبلغ المتاح من رصيد الموظف والخزينة
    const availableFromEmployee = Math.min(val, Math.max(0, employee.balance));
    const overdraftAmount = Math.max(0, val - employee.balance);
    const amountFromCashier = availableFromEmployee + overdraftAmount;

    // التحقق من رصيد الخزينة للمبلغ المطلوب
    if (amountFromCashier > cashierBalance) {
      toast({
        title: "تحذير",
        description: `الرصيد غير كافٍ في الخزينة. المطلوب: ${currency(amountFromCashier)}, المتاح: ${currency(cashierBalance)}`,
        variant: "destructive"
      });
      return;
    }

    // تحذير في حالة السحب على المكشوف
    if (overdraftAmount > 0) {
      const confirmMessage = `تحذير: سيتم سحب ${currency(overdraftAmount)} على المكشوف من الخزينة. هل تريد المتابعة؟`;
      if (!confirm(confirmMessage)) {
        return;
      }
    }

    if (isElectron && api) {
      try {
        const newWithdraw = await api.addWithdraw({
          employeeId: empId,
          description: curr.desc.trim(),
          amount: val,
          date: (curr.date as Date).toISOString(),
        });

        // تحديث الموظف محلياً
        setEmployees((emps) =>
          emps.map((emp) =>
            emp.id === empId
              ? {
                  ...emp,
                  balance: Math.max(0, emp.balance - val),
                  withdraws: [...emp.withdraws, newWithdraw],
                  overdraftRecords: overdraftAmount > 0 ? [
                    ...(emp.overdraftRecords || []),
                    {
                      id: (emp.overdraftRecords?.length || 0) === 0 ? 1 : Math.max(...(emp.overdraftRecords || []).map(o => o.id)) + 1,
                      amount: overdraftAmount,
                      withdrawId: newWithdraw.id,
                      date: new Date().toISOString(),
                      remainingAmount: overdraftAmount,
                      status: 'pending' as const,
                      payments: []
                    }
                  ] : (emp.overdraftRecords || [])
                }
              : emp
          )
        );
      } catch (error) {
        console.error('Error adding withdraw:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء إضافة السحبة.", variant: "destructive" });
        return;
      }
    } else {
      // إضافة السحب وتحديث رصيد الموظف
      const newWithdrawId = employees.find(e => e.id === empId)?.withdraws.length === 0 ? 1 :
        Math.max(...(employees.find(e => e.id === empId)?.withdraws.map((w) => w.id) || [0])) + 1;

      const newEmployees = employees.map((emp) =>
        emp.id === empId
          ? {
              ...emp,
              balance: Math.max(0, emp.balance - val),
              withdraws: [
                ...emp.withdraws,
                {
                  id: newWithdrawId,
                  desc: curr.desc.trim(),
                  amount: val,
                  date: (curr.date as Date).toISOString(),
                },
              ],
              overdraftRecords: overdraftAmount > 0 ? [
                ...(emp.overdraftRecords || []),
                {
                  id: (emp.overdraftRecords?.length || 0) === 0 ? 1 : Math.max(...(emp.overdraftRecords || []).map(o => o.id)) + 1,
                  amount: overdraftAmount,
                  withdrawId: newWithdrawId,
                  date: new Date().toISOString(),
                  remainingAmount: overdraftAmount,
                  status: 'pending' as const,
                  payments: []
                }
              ] : (emp.overdraftRecords || [])
            }
          : emp
      );
      setEmployees(newEmployees);
      saveEmployees(newEmployees);
    }

    // خصم المبلغ الكامل من الخزينة (رصيد الموظف + السحب على المكشوف)
    deductFromCashier(amountFromCashier);

    const finalBalance = Math.max(0, employee.balance - val);
    const overdraftMessage = overdraftAmount > 0 ? ` (سحب على المكشوف: ${currency(overdraftAmount)})` : "";

    toast({
      title: "تمت السحبة",
      description: `تم سحب ${currency(val)} للموظف ${employee.name}. الرصيد الجديد: ${currency(finalBalance)}${overdraftMessage}`
    });
    setNewWithdraws((inps) => ({ ...inps, [empId]: { desc: "", amount: "", date: null } }));
  };

  const addEmployee = async (name: string, balance: number = 0, maxLimit: number = 0, fixedSalary: number = 0) => {
    if (!name.trim()) {
      toast({ title: "تحذير", description: "يرجى إدخال اسم الموظف.", variant: "destructive" });
      return;
    }
    if (fixedSalary <= 0) {
      toast({ title: "تحذير", description: "يجب إدخال مرتب أساسي صحيح.", variant: "destructive" });
      return;
    }

    if (isElectron && api) {
      try {
        const newEmployee = await api.addEmployee({
          name: name.trim(),
          balance: fixedSalary, // الرصيد الابتدائي = المرتب الأساسي
          maxLimit: 0, // لم يعد مستخدماً
          fixedSalary,
        });
        setEmployees((prev) => [...prev, newEmployee]);
      } catch (error) {
        console.error('Error adding employee:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء إضافة الموظف.", variant: "destructive" });
        return;
      }
    } else {
      const newEmployee = {
        id: employees.length === 0 ? 1 : Math.max(...employees.map(e => e.id)) + 1,
        name: name.trim(),
        balance: fixedSalary, // الرصيد الابتدائي = المرتب الأساسي
        maxLimit: 0, // لم يعد مستخدماً
        fixedSalary,
        withdraws: [],
        balanceTransactions: [{
          id: 1,
          amount: fixedSalary,
          notes: "المرتب الأساسي",
          date: new Date().toISOString(),
          type: 'deposit' as const
        }],
        salaryDeductions: [],
        additions: [], // إضافة حقل الإضافات الجديد
        overdraftRecords: [] // إضافة حقل السحب على المكشوف
      };

      const newEmployees = [...employees, newEmployee];
      setEmployees(newEmployees);
      saveEmployees(newEmployees);
    }

    toast({ title: "تمت الإضافة", description: `تمت إضافة الموظف "${name}" بمرتب أساسي ${currency(fixedSalary)}` });
  };

  const removeEmployee = async (id: number) => {
    const employee = employees.find(e => e.id === id);
    if (!employee) return;

    if (isElectron && api) {
      try {
        await api.removeEmployee(id);
        setEmployees((prev) => prev.filter((e) => e.id !== id));
      } catch (error) {
        console.error('Error removing employee:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء حذف الموظف.", variant: "destructive" });
        return;
      }
    } else {
      const newEmployees = employees.filter((e) => e.id !== id);
      setEmployees(newEmployees);
      saveEmployees(newEmployees);
    }

    toast({ title: "تم الحذف", description: `تم حذف الموظف "${employee.name}" بنجاح.` });
  };

  const updateEmployeeBalance = async (id: number, amount: number, notes: string = "") => {
    if (isElectron && api) {
      try {
        const newTransaction = await api.updateEmployeeBalance({
          id,
          amount,
          notes,
        });

        setEmployees((prev) =>
          prev.map((emp) =>
            emp.id === id ? {
              ...emp,
              balance: emp.balance + amount,
              balanceTransactions: [...emp.balanceTransactions, newTransaction]
            } : emp
          )
        );
      } catch (error) {
        console.error('Error updating employee balance:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء تحديث رصيد الموظف.", variant: "destructive" });
        return;
      }
    } else {
      const newEmployees = employees.map((emp) =>
        emp.id === id ? {
          ...emp,
          balance: emp.balance + amount,
          balanceTransactions: [
            ...emp.balanceTransactions,
            {
              id: emp.balanceTransactions.length === 0 ? 1 : Math.max(...emp.balanceTransactions.map(t => t.id)) + 1,
              amount: Math.abs(amount),
              notes: notes || (amount > 0 ? "إيداع في الرصيد" : "خصم من الرصيد"),
              date: new Date().toISOString(),
              type: amount > 0 ? 'deposit' : 'deduction'
            }
          ]
        } : emp
      );
      setEmployees(newEmployees);
      saveEmployees(newEmployees);
    }

    const employee = employees.find(e => e.id === id);
    if (employee) {
      toast({
        title: "تم تحديث الرصيد",
        description: `تم تحديث رصيد ${employee.name} بقيمة ${currency(amount)}`
      });
    }
  };

  const getEmployeeMonthlyWithdraws = (empId: number, year: number, month: number) => {
    const employee = employees.find(emp => emp.id === empId);
    if (!employee) return [];

    return employee.withdraws.filter(withdraw => {
      const withdrawDate = new Date(withdraw.date);
      return withdrawDate.getFullYear() === year && withdrawDate.getMonth() === month;
    });
  };

  // حساب إجمالي الإيداعات للموظف
  const getEmployeeTotalDeposits = (empId: number) => {
    const employee = employees.find(e => e.id === empId);
    if (!employee) return 0;

    return employee.balanceTransactions
      .filter(transaction => transaction.type === 'deposit')
      .reduce((total, transaction) => total + transaction.amount, 0);
  };

  // حساب إجمالي السحوبات للموظف
  const getEmployeeTotalWithdraws = (empId: number) => {
    const employee = employees.find(e => e.id === empId);
    if (!employee) return 0;

    return employee.withdraws.reduce((total, withdraw) => total + withdraw.amount, 0);
  };

  // حساب إجمالي الإضافات للموظف
  const getEmployeeTotalAdditions = (empId: number) => {
    const employee = employees.find(e => e.id === empId);
    if (!employee || !employee.additions) return 0;

    return employee.additions.reduce((total, addition) => total + addition.amount, 0);
  };

  // حساب الرصيد المتاح للموظف (الراتب الأساسي + الإضافات - المسحوبات)
  const getEmployeeAvailableBalance = (empId: number) => {
    const employee = employees.find(e => e.id === empId);
    if (!employee) return 0;

    const fixedSalary = employee.fixedSalary || 0;
    const totalAdditions = getEmployeeTotalAdditions(empId);
    const totalWithdraws = getEmployeeTotalWithdraws(empId);

    return fixedSalary + totalAdditions - totalWithdraws;
  };

  // حساب إجمالي السحب على المكشوف المتبقي
  const getEmployeeTotalOverdraft = (empId: number) => {
    const employee = employees.find(e => e.id === empId);
    if (!employee || !employee.overdraftRecords) return 0;

    return employee.overdraftRecords
      .filter(record => record.status !== 'fully_paid')
      .reduce((total, record) => total + record.remainingAmount, 0);
  };

  // معالجة سداد السحب على المكشوف
  const processOverdraftPayment = (overdraftRecords: OverdraftRecord[], paymentAmount: number) => {
    let remainingPayment = paymentAmount;

    return overdraftRecords.map(record => {
      if (remainingPayment <= 0 || record.status === 'fully_paid') {
        return record;
      }

      const paymentForThisRecord = Math.min(remainingPayment, record.remainingAmount);
      remainingPayment -= paymentForThisRecord;

      const newPayment = {
        id: record.payments.length === 0 ? 1 : Math.max(...record.payments.map(p => p.id)) + 1,
        amount: paymentForThisRecord,
        date: new Date().toISOString(),
        source: 'addition' as const,
        notes: 'سداد تلقائي من الإضافة'
      };

      const newRemainingAmount = record.remainingAmount - paymentForThisRecord;
      const newStatus = newRemainingAmount === 0 ? 'fully_paid' :
                       record.payments.length === 0 ? 'partially_paid' : record.status;

      return {
        ...record,
        remainingAmount: newRemainingAmount,
        status: newStatus,
        payments: [...record.payments, newPayment]
      };
    });
  };

  // إضافة مبلغ للموظف (علاوة، مكافأة، إلخ)
  const addEmployeeAddition = async (empId: number, amount: number, reason: string, deductFromCashier: (amount: number) => void, autoPayOverdraft: boolean = false) => {
    if (amount <= 0) {
      toast({ title: "تحذير", description: "يجب أن يكون المبلغ أكبر من صفر.", variant: "destructive" });
      return;
    }

    if (!reason.trim()) {
      toast({ title: "تحذير", description: "يرجى إدخال سبب الإضافة.", variant: "destructive" });
      return;
    }

    const employee = employees.find(e => e.id === empId);
    if (!employee) {
      toast({ title: "خطأ", description: "الموظف غير موجود.", variant: "destructive" });
      return;
    }

    // حساب السحب على المكشوف المتبقي
    const totalOverdraft = getEmployeeTotalOverdraft(empId);
    const amountForOverdraft = autoPayOverdraft ? Math.min(amount, totalOverdraft) : 0;
    const amountForEmployee = amount - amountForOverdraft;

    if (isElectron && api) {
      try {
        const newAddition = await api.addEmployeeAddition({
          employeeId: empId,
          amount,
          reason: reason.trim(),
          autoPayOverdraft
        });

        // تحديث الموظف محلياً مع السداد التلقائي
        setEmployees((emps) =>
          emps.map((emp) =>
            emp.id === empId
              ? {
                  ...emp,
                  balance: emp.balance + amountForEmployee,
                  additions: [...(emp.additions || []), newAddition],
                  balanceTransactions: [
                    ...emp.balanceTransactions,
                    {
                      id: emp.balanceTransactions.length === 0 ? 1 : Math.max(...emp.balanceTransactions.map(t => t.id)) + 1,
                      amount,
                      notes: `إضافة: ${reason.trim()}`,
                      date: new Date().toISOString(),
                      type: 'deposit' as const
                    }
                  ],
                  overdraftRecords: amountForOverdraft > 0 ?
                    processOverdraftPayment(emp.overdraftRecords || [], amountForOverdraft) :
                    (emp.overdraftRecords || [])
                }
              : emp
          )
        );
      } catch (error) {
        console.error('Error adding employee addition:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء إضافة المبلغ.", variant: "destructive" });
        return;
      }
    } else {
      const newAddition = {
        id: Date.now(),
        amount,
        reason: reason.trim(),
        date: new Date().toISOString(),
      };

      const newEmployees = employees.map((emp) =>
        emp.id === empId
          ? {
              ...emp,
              balance: emp.balance + amountForEmployee,
              additions: [...(emp.additions || []), newAddition],
              balanceTransactions: [
                ...emp.balanceTransactions,
                {
                  id: emp.balanceTransactions.length === 0 ? 1 : Math.max(...emp.balanceTransactions.map(t => t.id)) + 1,
                  amount,
                  notes: `إضافة: ${reason.trim()}`,
                  date: new Date().toISOString(),
                  type: 'deposit' as const
                }
              ],
              overdraftRecords: amountForOverdraft > 0 ?
                processOverdraftPayment(emp.overdraftRecords || [], amountForOverdraft) :
                (emp.overdraftRecords || [])
            }
          : emp
      );
      setEmployees(newEmployees);
      saveEmployees(newEmployees);
    }

    // خصم المبلغ من الخزينة
    deductFromCashier(amount);

    // رسالة توضح التوزيع
    let message = `تم إضافة ${currency(amount)} للموظف ${employee.name} (${reason.trim()})`;
    if (autoPayOverdraft && amountForOverdraft > 0) {
      message += `\n- سداد السحب على المكشوف: ${currency(amountForOverdraft)}`;
      message += `\n- إضافة للرصيد: ${currency(amountForEmployee)}`;
    } else if (!autoPayOverdraft && totalOverdraft > 0) {
      message += `\n- تم إضافة المبلغ كاملاً للرصيد`;
      message += `\n- يمكن سداد السحب على المكشوف (${currency(totalOverdraft)}) لاحقاً`;
    }

    toast({
      title: "تمت الإضافة",
      description: message
    });
  };

  // سداد مبلغ من السحب على المكشوف يدوياً
  const payOverdraftAmount = async (empId: number, overdraftId: number, amount: number) => {
    const employee = employees.find(e => e.id === empId);
    if (!employee) {
      toast({ title: "خطأ", description: "الموظف غير موجود.", variant: "destructive" });
      return;
    }

    const overdraftRecord = employee.overdraftRecords?.find(r => r.id === overdraftId);
    if (!overdraftRecord) {
      toast({ title: "خطأ", description: "سجل السحب على المكشوف غير موجود.", variant: "destructive" });
      return;
    }

    if (amount <= 0 || amount > overdraftRecord.remainingAmount) {
      toast({ title: "تحذير", description: "المبلغ غير صحيح.", variant: "destructive" });
      return;
    }

    if (amount > employee.balance) {
      toast({ title: "تحذير", description: "رصيد الموظف غير كافٍ للسداد.", variant: "destructive" });
      return;
    }

    if (isElectron && api) {
      try {
        await api.payOverdraftAmount({
          employeeId: empId,
          overdraftId,
          amount
        });

        // تحديث الموظف محلياً
        setEmployees((emps) =>
          emps.map((emp) =>
            emp.id === empId
              ? {
                  ...emp,
                  balance: emp.balance - amount,
                  overdraftRecords: (emp.overdraftRecords || []).map(record =>
                    record.id === overdraftId
                      ? {
                          ...record,
                          remainingAmount: record.remainingAmount - amount,
                          status: (record.remainingAmount - amount) === 0 ? 'fully_paid' as const :
                                  record.payments.length === 0 ? 'partially_paid' as const : record.status,
                          payments: [
                            ...record.payments,
                            {
                              id: record.payments.length === 0 ? 1 : Math.max(...record.payments.map(p => p.id)) + 1,
                              amount,
                              date: new Date().toISOString(),
                              source: 'manual' as const,
                              notes: 'سداد يدوي'
                            }
                          ]
                        }
                      : record
                  )
                }
              : emp
          )
        );
      } catch (error) {
        console.error('Error paying overdraft amount:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء السداد.", variant: "destructive" });
        return;
      }
    } else {
      const newEmployees = employees.map((emp) =>
        emp.id === empId
          ? {
              ...emp,
              balance: emp.balance - amount,
              overdraftRecords: (emp.overdraftRecords || []).map(record =>
                record.id === overdraftId
                  ? {
                      ...record,
                      remainingAmount: record.remainingAmount - amount,
                      status: (record.remainingAmount - amount) === 0 ? 'fully_paid' as const :
                              record.payments.length === 0 ? 'partially_paid' as const : record.status,
                      payments: [
                        ...record.payments,
                        {
                          id: record.payments.length === 0 ? 1 : Math.max(...record.payments.map(p => p.id)) + 1,
                          amount,
                          date: new Date().toISOString(),
                          source: 'manual' as const,
                          notes: 'سداد يدوي'
                        }
                      ]
                    }
                  : record
              )
            }
          : emp
      );

      setEmployees(newEmployees);
      saveEmployees(newEmployees);
    }

    toast({
      title: "تم السداد",
      description: `تم سداد ${currency(amount)} من السحب على المكشوف للموظف ${employee.name}`
    });
  };

  // إضافة خصم من المرتب
  const addSalaryDeduction = async (empId: number, amount: number, reason: string, month: string) => {
    const newDeduction = {
      id: Date.now(),
      amount,
      reason,
      month,
      date: new Date().toISOString(),
    };

    if (isElectron && api) {
      // سيتم إضافة API لاحقاً
      const newEmployees = employees.map((emp) =>
        emp.id === empId
          ? { ...emp, salaryDeductions: [...(emp.salaryDeductions || []), newDeduction] }
          : emp
      );
      setEmployees(newEmployees);
    } else {
      const newEmployees = employees.map((emp) =>
        emp.id === empId
          ? { ...emp, salaryDeductions: [...(emp.salaryDeductions || []), newDeduction] }
          : emp
      );
      setEmployees(newEmployees);
      saveEmployees(newEmployees);
    }

    toast({
      title: "تم إضافة خصم المرتب",
      description: `تم خصم ${currency(amount)} من مرتب الموظف لشهر ${month}`
    });
  };

  return {
    employees,
    newWithdraws,
    handleNewWithdrawChange,
    handleAddWithdraw,
    addEmployee,
    removeEmployee,
    updateEmployeeBalance,
    getEmployeeMonthlyWithdraws,
    getEmployeeTotalDeposits,
    getEmployeeTotalWithdraws,
    getEmployeeTotalAdditions,
    getEmployeeAvailableBalance,
    getEmployeeTotalOverdraft,
    addEmployeeAddition,
    payOverdraftAmount,
    addSalaryDeduction,
  };
};
