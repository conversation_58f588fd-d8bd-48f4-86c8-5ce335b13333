
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Printer, User, TrendingUp, TrendingDown } from "lucide-react";
import { Employee } from "@/types";

interface EmployeeReportProps {
  employee: Employee;
  currency: (amount: number) => string;
}

const EmployeeReport = ({ employee, currency }: EmployeeReportProps) => {
  const handlePrint = () => {
    const printContent = document.getElementById(`employee-report-${employee.id}`);
    if (printContent) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>تقرير الموظف - ${employee.name}</title>
              <style>
                body { font-family: Aria<PERSON>, sans-serif; direction: rtl; margin: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; }
                .header { text-align: center; margin-bottom: 30px; }
                .summary { margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 8px; }
                .print-date { text-align: left; margin-bottom: 20px; font-size: 12px; color: #666; }
              </style>
            </head>
            <body>
              <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString("ar-LY")}</div>
              ${printContent.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  const totalWithdrawals = employee.withdraws.reduce((sum, w) => sum + w.amount, 0);
  const totalDeposits = employee.balanceTransactions
    .filter(t => t.type === 'deposit')
    .reduce((sum, t) => sum + t.amount, 0);

  // دمج جميع المعاملات وترتيبها حسب التاريخ
  const allTransactions = [
    ...employee.withdraws.map(w => ({
      type: "سحب",
      desc: w.desc,
      amount: w.amount,
      date: w.date,
      id: `W-${w.id}`,
      isDebit: true
    })),
    ...employee.balanceTransactions.map(t => ({
      type: t.type === 'deposit' ? "إيداع رصيد" : "خصم رصيد",
      desc: t.notes,
      amount: t.amount,
      date: t.date,
      id: `BT-${t.id}`,
      isDebit: t.type === 'deduction'
    }))
  ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <User className="w-5 h-5" />
          <CardTitle>تقرير الموظف: {employee.name}</CardTitle>
        </div>
        <Button onClick={handlePrint} variant="outline" size="sm">
          <Printer className="w-4 h-4 mr-2" />
          طباعة
        </Button>
      </CardHeader>
      <CardContent>
        <div id={`employee-report-${employee.id}`}>
          <div className="header">
            <h2 className="text-2xl font-bold">تقرير مالي مفصل</h2>
            <h3 className="text-xl">الموظف: {employee.name}</h3>
          </div>

          {/* ملخص الحساب */}
          <div className="summary grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">الرصيد الحالي</p>
                  <p className="text-lg font-semibold text-blue-600">{currency(employee.balance)}</p>
                </div>
              </div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">إجمالي الإيداعات</p>
                  <p className="text-lg font-semibold text-green-600">{currency(totalDeposits)}</p>
                </div>
              </div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <TrendingDown className="w-4 h-4 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">إجمالي السحوبات</p>
                  <p className="text-lg font-semibold text-red-600">{currency(totalWithdrawals)}</p>
                </div>
              </div>
            </div>
          </div>

          {/* تفاصيل المعاملات */}
          <div className="overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>نوع المعاملة</TableHead>
                  <TableHead>الوصف</TableHead>
                  <TableHead>المبلغ</TableHead>
                  <TableHead>رقم المعاملة</TableHead>
                  <TableHead>التاريخ</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {allTransactions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center text-gray-500 py-8">
                      لا توجد معاملات لهذا الموظف
                    </TableCell>
                  </TableRow>
                ) : (
                  allTransactions.map((transaction, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Badge 
                          variant={
                            transaction.type === "سحب" ? "destructive" :
                            transaction.type === "إيداع رصيد" ? "secondary" : "outline"
                          }
                        >
                          {transaction.type}
                        </Badge>
                      </TableCell>
                      <TableCell>{transaction.desc}</TableCell>
                      <TableCell className={`font-mono ${transaction.isDebit ? 'text-red-600' : 'text-green-600'}`}>
                        {currency(transaction.amount)}
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">{transaction.id}</TableCell>
                      <TableCell>
                        {new Date(transaction.date).toLocaleDateString("ar-LY")}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EmployeeReport;
