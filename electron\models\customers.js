const { db } = require('../database');

class CustomerModel {
  // الحصول على جميع العملاء مع بياناتهم
  static getAllCustomers() {
    const customers = db.prepare(`
      SELECT * FROM customers ORDER BY name
    `).all();

    return customers.map(customer => ({
      ...customer,
      payments: this.getCustomerPayments(customer.id)
    }));
  }

  // الحصول على عميل واحد
  static getCustomerById(id) {
    const customer = db.prepare(`
      SELECT * FROM customers WHERE id = ?
    `).get(id);

    if (!customer) return null;

    return {
      ...customer,
      payments: this.getCustomerPayments(id)
    };
  }

  // إضافة عميل جديد
  static createCustomer(name, phone = '', address = '') {
    const stmt = db.prepare(`
      INSERT INTO customers (name, phone, address)
      VALUES (?, ?, ?)
    `);
    
    const result = stmt.run(name, phone, address);
    return this.getCustomerById(result.lastInsertRowid);
  }

  // تحديث عميل
  static updateCustomer(id, data) {
    const stmt = db.prepare(`
      UPDATE customers 
      SET name = ?, phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    
    stmt.run(data.name, data.phone, data.address, id);
    return this.getCustomerById(id);
  }

  // حذف عميل
  static deleteCustomer(id) {
    const stmt = db.prepare(`DELETE FROM customers WHERE id = ?`);
    return stmt.run(id);
  }

  // الحصول على مدفوعات العميل
  static getCustomerPayments(customerId) {
    return db.prepare(`
      SELECT * FROM customer_payments 
      WHERE customer_id = ? 
      ORDER BY payment_date DESC
    `).all(customerId).map(payment => ({
      id: payment.id,
      amount: payment.amount,
      type: payment.type,
      paymentDate: payment.payment_date,
      notes: payment.notes
    }));
  }

  // إضافة دفعة للعميل
  static addCustomerPayment(customerId, amount, type, paymentDate, notes = '') {
    const stmt = db.prepare(`
      INSERT INTO customer_payments (customer_id, amount, type, payment_date, notes)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(customerId, amount, type, paymentDate, notes);
    
    return {
      id: result.lastInsertRowid,
      amount,
      type,
      paymentDate,
      notes
    };
  }

  // حذف دفعة
  static deleteCustomerPayment(paymentId) {
    const stmt = db.prepare(`DELETE FROM customer_payments WHERE id = ?`);
    return stmt.run(paymentId);
  }
}

module.exports = CustomerModel;
