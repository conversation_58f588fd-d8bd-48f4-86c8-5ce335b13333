const fs = require('fs');
const path = require('path');
const { app } = require('electron');

const isDev = process.env.NODE_ENV === 'development';

// إعداد مجلد البيانات
let dataPath;

function setupDataPath() {
  dataPath = isDev
    ? path.join(__dirname, '..', 'data')
    : path.join(app.getPath('userData'), 'data');

  // إنشاء مجلد البيانات إذا لم يكن موجوداً
  if (!fs.existsSync(dataPath)) {
    fs.mkdirSync(dataPath, { recursive: true });
  }
}

// قراءة البيانات من ملف JSON
function readData(filename) {
  const filePath = path.join(dataPath, `${filename}.json`);
  try {
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
  }
  return null;
}

// كتابة البيانات إلى ملف JSON
function writeData(filename, data) {
  const filePath = path.join(dataPath, `${filename}.json`);
  try {
    // إنشاء نسخة احتياطية قبل الكتابة
    if (fs.existsSync(filePath)) {
      const backupPath = filePath + '.backup';
      fs.copyFileSync(filePath, backupPath);
    }
    
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error(`Error writing ${filename}:`, error);
    
    // استعادة النسخة الاحتياطية في حالة الفشل
    const backupPath = filePath + '.backup';
    if (fs.existsSync(backupPath)) {
      try {
        fs.copyFileSync(backupPath, filePath);
        console.log(`Restored backup for ${filename}`);
      } catch (restoreError) {
        console.error(`Failed to restore backup for ${filename}:`, restoreError);
      }
    }
    
    return false;
  }
}

// قراءة البيانات مع قيم افتراضية
function readDataWithDefaults(filename, defaultValue = []) {
  const data = readData(filename);
  return data !== null ? data : defaultValue;
}

// كتابة البيانات مع التحقق من الصحة
function writeDataSafe(filename, data, validator = null) {
  try {
    // التحقق من صحة البيانات إذا تم توفير validator
    if (validator && !validator(data)) {
      throw new Error(`Data validation failed for ${filename}`);
    }
    
    return writeData(filename, data);
  } catch (error) {
    console.error(`Safe write failed for ${filename}:`, error);
    return false;
  }
}

// تنظيف الملفات الاحتياطية القديمة
function cleanupBackups() {
  try {
    const files = fs.readdirSync(dataPath);
    const backupFiles = files.filter(file => file.endsWith('.backup'));
    
    backupFiles.forEach(file => {
      const filePath = path.join(dataPath, file);
      const stats = fs.statSync(filePath);
      const ageInDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
      
      // حذف النسخ الاحتياطية الأقدم من 7 أيام
      if (ageInDays > 7) {
        fs.unlinkSync(filePath);
        console.log(`Cleaned up old backup: ${file}`);
      }
    });
  } catch (error) {
    console.error('Error cleaning up backups:', error);
  }
}

// إنشاء نسخة احتياطية شاملة
function createFullBackup() {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(dataPath, `backup-${timestamp}`);
    
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const files = fs.readdirSync(dataPath);
    const dataFiles = files.filter(file => file.endsWith('.json') && !file.endsWith('.backup'));
    
    dataFiles.forEach(file => {
      const sourcePath = path.join(dataPath, file);
      const destPath = path.join(backupDir, file);
      fs.copyFileSync(sourcePath, destPath);
    });
    
    console.log(`Full backup created: ${backupDir}`);
    return backupDir;
  } catch (error) {
    console.error('Error creating full backup:', error);
    return null;
  }
}

// استعادة من نسخة احتياطية
function restoreFromBackup(backupDir) {
  try {
    if (!fs.existsSync(backupDir)) {
      throw new Error(`Backup directory not found: ${backupDir}`);
    }
    
    const files = fs.readdirSync(backupDir);
    const dataFiles = files.filter(file => file.endsWith('.json'));
    
    dataFiles.forEach(file => {
      const sourcePath = path.join(backupDir, file);
      const destPath = path.join(dataPath, file);
      fs.copyFileSync(sourcePath, destPath);
    });
    
    console.log(`Data restored from backup: ${backupDir}`);
    return true;
  } catch (error) {
    console.error('Error restoring from backup:', error);
    return false;
  }
}

// التحقق من سلامة البيانات
function validateDataIntegrity() {
  const results = {};
  
  try {
    // التحقق من الموظفين
    const employees = readData('employees');
    results.employees = {
      valid: Array.isArray(employees),
      count: employees ? employees.length : 0,
      issues: []
    };
    
    if (employees) {
      employees.forEach((emp, index) => {
        if (!emp.id || !emp.name) {
          results.employees.issues.push(`Employee ${index}: Missing required fields`);
        }
        if (typeof emp.balance !== 'number') {
          results.employees.issues.push(`Employee ${index}: Invalid balance type`);
        }
      });
    }
    
    // التحقق من العملاء
    const customers = readData('customers');
    results.customers = {
      valid: Array.isArray(customers),
      count: customers ? customers.length : 0,
      issues: []
    };
    
    // التحقق من الأقسام
    const sections = readData('sections');
    results.sections = {
      valid: Array.isArray(sections),
      count: sections ? sections.length : 0,
      issues: []
    };
    
    // التحقق من الخزينة
    const cashier = readData('cashier');
    results.cashier = {
      valid: cashier && typeof cashier.balance === 'number',
      balance: cashier ? cashier.balance : 0,
      issues: []
    };
    
    console.log('Data integrity check completed:', results);
    return results;
    
  } catch (error) {
    console.error('Error validating data integrity:', error);
    return { error: error.message };
  }
}

// تهيئة نظام التخزين
function initializeStorage() {
  setupDataPath();
  cleanupBackups();
  
  // إنشاء نسخة احتياطية عند بدء التشغيل
  createFullBackup();
  
  // التحقق من سلامة البيانات
  const integrity = validateDataIntegrity();
  
  console.log('Storage system initialized');
  return integrity;
}

module.exports = {
  setupDataPath,
  readData,
  writeData,
  readDataWithDefaults,
  writeDataSafe,
  cleanupBackups,
  createFullBackup,
  restoreFromBackup,
  validateDataIntegrity,
  initializeStorage
};
