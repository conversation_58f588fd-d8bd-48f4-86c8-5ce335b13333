# دليل البناء والتوزيع

## نظرة عامة

هذا الدليل يوضح كيفية بناء وتوزيع تطبيق نظام إدارة مصنع الأثاث المالي كتطبيق سطح مكتب.

## متطلبات البناء

### البرامج المطلوبة
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn
- Git

### نظم التشغيل المدعومة
- Windows 10/11 (64-bit)
- macOS 10.15+ (اختياري)
- Linux Ubuntu 18.04+ (اختياري)

## خطوات البناء

### 1. إعداد البيئة

```bash
# استنساخ المشروع
git clone [repository-url]
cd furniture-factory-financial-system

# تثبيت المكتبات
npm install

# إعادة بناء المكتبات الأصلية لـ Electron
npx electron-rebuild
```

### 2. التطوير والاختبار

```bash
# تشغيل في وضع التطوير
npm run electron:dev

# تشغيل الواجهة فقط (للاختبار)
npm run dev

# تشغيل Electron مع البناء الحالي
npm run electron
```

### 3. البناء للإنتاج

```bash
# بناء الواجهة الأمامية
npm run build

# بناء تطبيق Electron
npm run build:electron

# بناء وتوزيع (إنشاء ملف التثبيت)
npm run dist
```

## ملفات الإخراج

بعد البناء الناجح، ستجد الملفات في:

### مجلد `dist/`
- ملفات الواجهة الأمامية المبنية
- `index.html` - الملف الرئيسي
- `assets/` - ملفات CSS و JavaScript

### مجلد `dist-electron/`
- ملفات التطبيق المعبأة
- `win-unpacked/` - التطبيق غير المضغوط (Windows)
- `*.exe` - ملف التثبيت (Windows)

## إعدادات البناء

### ملف `package.json`
```json
{
  "build": {
    "appId": "com.furniture-factory.financial-system",
    "productName": "نظام إدارة مصنع الأثاث المالي",
    "directories": {
      "output": "dist-electron"
    },
    "files": [
      "dist/**/*",
      "electron/**/*",
      "node_modules/**/*",
      "package.json"
    ],
    "win": {
      "target": "nsis",
      "icon": "public/favicon.ico"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true
    }
  }
}
```

## حل المشاكل الشائعة

### مشكلة better-sqlite3
```bash
# إعادة بناء المكتبة
npm rebuild better-sqlite3

# أو استخدام electron-rebuild
npx electron-rebuild
```

### مشكلة ES Modules
- تأكد من وجود `electron/package.json` مع `"type": "commonjs"`
- استخدم `require()` في ملفات Electron

### مشكلة المسارات
- استخدم `HashRouter` بدلاً من `BrowserRouter`
- تأكد من `base: "./"` في `vite.config.ts`

## التوزيع

### إنشاء ملف التثبيت

```bash
# للويندوز
npm run dist

# تخصيص المنصة
npx electron-builder --win
npx electron-builder --mac
npx electron-builder --linux
```

### رفع الإصدار

1. تحديث رقم الإصدار في `package.json`
2. إنشاء tag في Git
3. بناء التطبيق
4. رفع ملفات التوزيع

```bash
# تحديث الإصدار
npm version patch  # أو minor أو major

# إنشاء tag
git tag v1.0.0
git push origin v1.0.0

# البناء والتوزيع
npm run dist
```

## اختبار التطبيق

### اختبار محلي
```bash
# تشغيل في وضع التطوير
npm run electron:dev

# اختبار البناء
npm run build
npm run electron
```

### اختبار التثبيت
1. بناء ملف التثبيت
2. تثبيت على جهاز نظيف
3. اختبار جميع الوظائف
4. التأكد من حفظ البيانات

## الأمان

### توقيع الكود (اختياري)
```bash
# إعداد شهادة التوقيع
export CSC_LINK="path/to/certificate.p12"
export CSC_KEY_PASSWORD="certificate-password"

# البناء مع التوقيع
npm run dist
```

### فحص الأمان
- فحص المكتبات: `npm audit`
- فحص الثغرات: `npm audit fix`

## النشر التلقائي (CI/CD)

### GitHub Actions
```yaml
name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm run build
      - run: npm run dist
      - uses: actions/upload-artifact@v2
        with:
          name: windows-installer
          path: dist-electron/*.exe
```

## نصائح للأداء

1. **تحسين الحجم**:
   - استخدم `npm run build` قبل التوزيع
   - احذف المكتبات غير المستخدمة

2. **تحسين السرعة**:
   - استخدم lazy loading للمكونات
   - ضغط الصور والأصول

3. **تحسين الذاكرة**:
   - تجنب memory leaks
   - استخدم React.memo للمكونات الثقيلة

## الدعم

للمساعدة في البناء والتوزيع:
- 📖 وثائق Electron: https://electronjs.org/docs
- 📖 وثائق electron-builder: https://electron.build
- 🐛 تقارير المشاكل: [GitHub Issues]
