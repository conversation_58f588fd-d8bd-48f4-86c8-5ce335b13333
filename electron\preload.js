const { contextBridge, ipcRenderer } = require('electron');

// تعريض API آمن للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
  // الخزينة
  getCashierBalance: () => ipcRenderer.invoke('get-cashier-balance'),
  updateCashierBalance: (balance) => ipcRenderer.invoke('update-cashier-balance', balance),
  
  // الأقسام
  getSections: () => ipcRenderer.invoke('get-sections'),
  addSection: (name) => ipcRenderer.invoke('add-section', name),
  deleteSection: (id) => ipcRenderer.invoke('delete-section', id),
  addExpense: (data) => ipcRenderer.invoke('add-expense', data),
  
  // الموظفين
  getEmployees: () => ipcRenderer.invoke('get-employees'),
  addEmployee: (data) => ipcRenderer.invoke('add-employee', data),
  removeEmployee: (id) => ipcRenderer.invoke('remove-employee', id),
  updateEmployeeBalance: (data) => ipcRenderer.invoke('update-employee-balance', data),
  addWithdraw: (data) => ipcRenderer.invoke('add-withdraw', data),
  addEmployeeAddition: (data) => ipcRenderer.invoke('add-employee-addition', data),
  payOverdraftAmount: (data) => ipcRenderer.invoke('pay-overdraft-amount', data),

  // الخزينة المتقدمة
  getCashierDeposits: () => ipcRenderer.invoke('get-cashier-deposits'),
  addAdvancedDeposit: (data) => ipcRenderer.invoke('add-advanced-deposit', data),

  // العملاء
  getCustomers: () => ipcRenderer.invoke('get-customers'),
  addCustomer: (data) => ipcRenderer.invoke('add-customer', data),
  updateCustomer: (id, data) => ipcRenderer.invoke('update-customer', id, data),
  removeCustomer: (id) => ipcRenderer.invoke('remove-customer', id),
  saveCustomers: (data) => ipcRenderer.invoke('save-customers', data),

  // المدفوعات
  getPayments: () => ipcRenderer.invoke('get-payments'),
  addPayment: (data) => ipcRenderer.invoke('add-payment', data)
});
