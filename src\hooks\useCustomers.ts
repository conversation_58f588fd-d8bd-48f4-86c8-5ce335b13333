import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { Customer, CashierDeposit, Payment } from "@/types";
import { useElectronAPI } from "./useElectronAPI";

const currency = (a: number) => a.toLocaleString("ar-LY") + " د.ل";

export const useCustomers = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const { isElectron, api } = useElectronAPI();

  // ربط الفواتير الموجودة بالعملاء
  const linkExistingInvoicesToCustomers = (customers: Customer[], existingInvoices: CashierDeposit[]) => {
    if (!customers || customers.length === 0) {
      console.log('No customers to link invoices to');
      return customers;
    }

    return customers.map(customer => {
      const customerInvoices = existingInvoices.filter(invoice =>
        invoice.customerId === customer.id || invoice.customerName === customer.name
      );

      const totalPaid = customerInvoices.reduce((sum, inv) => sum + inv.paidAmount, 0);
      const totalRemaining = customerInvoices.reduce((sum, inv) => sum + inv.remainingAmount, 0);

      return {
        ...customer,
        invoices: [...(customer.invoices || []), ...customerInvoices],
        totalInvoices: (customer.totalInvoices || 0) + customerInvoices.length,
        totalPaid: (customer.totalPaid || 0) + totalPaid,
        totalRemaining: (customer.totalRemaining || 0) + totalRemaining
      };
    });
  };

  // تحميل العملاء عند بدء التطبيق
  useEffect(() => {
    const loadCustomers = async () => {
      if (isElectron && api) {
        try {
          const loadedCustomers = await api.getCustomers();
          // التأكد من وجود الحقول المطلوبة
          const updatedCustomers = loadedCustomers.map((customer: Customer) => ({
            ...customer,
            invoices: customer.invoices || [],
            payments: customer.payments || []
          }));
          setCustomers(updatedCustomers);
        } catch (error) {
          console.error('Error loading customers:', error);
        }
      } else {
        // استخدام localStorage كبديل للمتصفح
        const savedCustomers = localStorage.getItem('customers');
        if (savedCustomers) {
          const parsedCustomers = JSON.parse(savedCustomers);
          // التأكد من وجود الحقول المطلوبة
          const updatedCustomers = parsedCustomers.map((customer: Customer) => ({
            ...customer,
            invoices: customer.invoices || [],
            payments: customer.payments || []
          }));
          setCustomers(updatedCustomers);
        }
      }
    };

    loadCustomers();
  }, [isElectron, api]);

  // حفظ العملاء
  const saveCustomers = async (newCustomers: Customer[]) => {
    console.log('Saving customers:', newCustomers.length, 'customers');
    if (isElectron && api) {
      try {
        const result = await api.saveCustomers(newCustomers);
        console.log('Customers saved to Electron successfully:', result);
      } catch (error) {
        console.error('Error saving customers to Electron:', error);
      }
    } else {
      localStorage.setItem('customers', JSON.stringify(newCustomers));
      console.log('Customers saved to localStorage');
    }
  };

  // إضافة عميل جديد
  const addCustomer = async (name: string, phone?: string, address?: string) => {
    if (!name.trim()) {
      toast({ title: "تحذير", description: "يرجى إدخال اسم العميل.", variant: "destructive" });
      return;
    }

    if (isElectron && api) {
      try {
        const newCustomer = await api.addCustomer({
          name: name.trim(),
          phone: phone?.trim() || '',
          address: address?.trim() || '',
        });
        setCustomers([...customers, newCustomer]);
      } catch (error) {
        console.error('Error adding customer:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء إضافة العميل.", variant: "destructive" });
        return;
      }
    } else {
      const newCustomer: Customer = {
        id: customers.length === 0 ? 1 : Math.max(...customers.map(c => c.id)) + 1,
        name: name.trim(),
        phone: phone?.trim() || '',
        address: address?.trim() || '',
        totalInvoices: 0,
        totalPaid: 0,
        totalRemaining: 0,
        invoices: [],
        payments: []
      };
      
      const newCustomers = [...customers, newCustomer];
      setCustomers(newCustomers);
      await saveCustomers(newCustomers);
    }

    toast({ title: "تمت الإضافة", description: `تمت إضافة العميل "${name}" بنجاح.` });
  };

  // تحديث بيانات العميل
  const updateCustomer = async (id: number, customerData: Partial<Customer>) => {
    if (isElectron && api) {
      try {
        const updatedCustomer = await api.updateCustomer(id, customerData);
        if (updatedCustomer) {
          setCustomers(customers.map(c => c.id === id ? updatedCustomer : c));
        }
      } catch (error) {
        console.error('Error updating customer:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء تحديث بيانات العميل.", variant: "destructive" });
        return;
      }
    } else {
      const newCustomers = customers.map(c =>
        c.id === id ? { ...c, ...customerData } : c
      );
      setCustomers(newCustomers);
      await saveCustomers(newCustomers);
    }

    toast({ title: "تم التحديث", description: "تم تحديث بيانات العميل بنجاح." });
  };

  // حذف عميل
  const removeCustomer = async (id: number) => {
    const customer = customers.find(c => c.id === id);
    if (!customer) return;

    if (isElectron && api) {
      try {
        await api.removeCustomer(id);
        setCustomers(customers.filter(c => c.id !== id));
      } catch (error) {
        console.error('Error removing customer:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء حذف العميل.", variant: "destructive" });
        return;
      }
    } else {
      const newCustomers = customers.filter(c => c.id !== id);
      setCustomers(newCustomers);
      await saveCustomers(newCustomers);
    }

    toast({ title: "تم الحذف", description: `تم حذف العميل "${customer.name}" بنجاح.` });
  };

  // الحصول على فواتير العميل
  const getCustomerInvoices = (customerId: number): CashierDeposit[] => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.invoices : [];
  };

  // حساب إجماليات العميل
  const calculateCustomerTotals = (customerId: number) => {
    const customer = customers.find(c => c.id === customerId);
    if (!customer) return { totalInvoices: 0, totalPaid: 0, totalRemaining: 0 };

    const totalInvoices = customer.invoices.length;
    const totalPaid = customer.invoices.reduce((sum, invoice) => sum + invoice.paidAmount, 0);
    const totalRemaining = customer.invoices.reduce((sum, invoice) => sum + invoice.remainingAmount, 0);

    return { totalInvoices, totalPaid, totalRemaining };
  };

  // إنشاء فاتورة جديدة للعميل
  const createInvoiceForCustomer = async (customerId: number, invoiceData: Omit<CashierDeposit, 'id' | 'date' | 'customerId' | 'customerName' | 'status'>) => {
    const customer = customers.find(c => c.id === customerId);
    if (!customer) {
      toast({ title: "خطأ", description: "العميل غير موجود.", variant: "destructive" });
      return;
    }

    const newInvoice: CashierDeposit = {
      id: Date.now(),
      customerId,
      customerName: customer.name,
      status: invoiceData.remainingAmount > 0 ? 'pending' : 'completed',
      date: new Date().toISOString(),
      ...invoiceData
    };

    // تحديث العميل بالفاتورة الجديدة
    const updatedCustomers = customers.map(c =>
      c.id === customerId
        ? {
            ...c,
            invoices: [...c.invoices, newInvoice],
            totalInvoices: c.totalInvoices + 1,
            totalPaid: c.totalPaid + invoiceData.paidAmount,
            totalRemaining: c.totalRemaining + invoiceData.remainingAmount
          }
        : c
    );

    setCustomers(updatedCustomers);
    await saveCustomers(updatedCustomers);

    toast({
      title: "تمت الإضافة",
      description: `تمت إضافة فاتورة رقم ${invoiceData.invoiceNumber} للعميل ${customer.name}`
    });
  };

  // تسجيل دفعة للعميل
  const makePayment = async (customerId: number, invoiceId: number, amount: number, type: 'partial' | 'full', notes?: string) => {
    const customer = customers.find(c => c.id === customerId);
    if (!customer) {
      toast({ title: "خطأ", description: "العميل غير موجود.", variant: "destructive" });
      return;
    }

    const invoice = customer.invoices.find(inv => inv.id === invoiceId);
    if (!invoice) {
      toast({ title: "خطأ", description: "الفاتورة غير موجودة.", variant: "destructive" });
      return;
    }

    if (amount > invoice.remainingAmount) {
      toast({ title: "خطأ", description: "المبلغ أكبر من المبلغ المتبقي.", variant: "destructive" });
      return;
    }

    const newPayment: Payment = {
      id: Date.now(),
      invoiceId,
      customerId,
      amount,
      paymentDate: new Date().toISOString(),
      notes,
      type
    };

    // تحديث الفاتورة والعميل
    const updatedCustomers = customers.map(c =>
      c.id === customerId
        ? {
            ...c,
            invoices: c.invoices.map(inv =>
              inv.id === invoiceId
                ? {
                    ...inv,
                    paidAmount: inv.paidAmount + amount,
                    remainingAmount: inv.remainingAmount - amount,
                    status: (inv.remainingAmount - amount) <= 0 ? 'completed' : 'pending'
                  }
                : inv
            ),
            payments: [...(c.payments || []), newPayment],
            totalPaid: c.totalPaid + amount,
            totalRemaining: c.totalRemaining - amount
          }
        : c
    );

    setCustomers(updatedCustomers);
    await saveCustomers(updatedCustomers);

    toast({
      title: "تم السداد",
      description: `تم تسجيل دفعة بقيمة ${currency(amount)} للعميل ${customer.name}`
    });

    return newPayment;
  };

  // الحصول على مدفوعات العميل
  const getCustomerPayments = (customerId: number): Payment[] => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? (customer.payments || []) : [];
  };

  // تحديث حالة الفاتورة
  const updateInvoiceStatus = async (invoiceId: number) => {
    const updatedCustomers = customers.map(customer => ({
      ...customer,
      invoices: customer.invoices.map(invoice =>
        invoice.id === invoiceId
          ? { ...invoice, status: invoice.remainingAmount <= 0 ? 'completed' : 'pending' }
          : invoice
      )
    }));

    setCustomers(updatedCustomers);
    await saveCustomers(updatedCustomers);
  };

  // تحديث العملاء مع الفواتير الموجودة
  const updateCustomersWithExistingInvoices = async (existingInvoices: CashierDeposit[]) => {
    if (!customers || customers.length === 0) {
      console.log('No customers to update with existing invoices');
      return;
    }

    console.log('Updating customers with existing invoices:', customers.length, 'customers');
    const updatedCustomers = linkExistingInvoicesToCustomers(customers, existingInvoices);
    setCustomers(updatedCustomers);
    // لا نحفظ هنا لتجنب الحلقة اللانهائية
    // await saveCustomers(updatedCustomers);
  };

  return {
    customers,
    payments,
    addCustomer,
    updateCustomer,
    removeCustomer,
    getCustomerInvoices,
    calculateCustomerTotals,
    createInvoiceForCustomer,
    makePayment,
    getCustomerPayments,
    updateInvoiceStatus,
    updateCustomersWithExistingInvoices,
  };
};
