export interface ElectronAPI {
  // الخزينة
  getCashierBalance: () => Promise<number>;
  updateCashierBalance: (balance: number) => Promise<boolean>;
  
  // الأقسام
  getSections: () => Promise<Section[]>;
  addSection: (name: string) => Promise<Section>;
  deleteSection: (id: number) => Promise<boolean>;
  addExpense: (data: {
    sectionId: number;
    description: string;
    amount: number;
    date: string;
  }) => Promise<Expense>;
  
  // الموظفين
  getEmployees: () => Promise<Employee[]>;
  addEmployee: (data: {
    name: string;
    balance: number;
    maxLimit: number;
    fixedSalary: number;
  }) => Promise<Employee>;
  removeEmployee: (id: number) => Promise<boolean>;
  updateEmployeeBalance: (data: {
    id: number;
    amount: number;
    notes?: string;
  }) => Promise<BalanceTransaction>;
  addWithdraw: (data: {
    employeeId: number;
    description: string;
    amount: number;
    date: string;
  }) => Promise<Withdraw>;
  addEmployeeAddition: (data: {
    employeeId: number;
    amount: number;
    reason: string;
  }) => Promise<EmployeeAddition>;

  // الخزينة المتقدمة
  getCashierDeposits: () => Promise<CashierDeposit[]>;
  addAdvancedDeposit: (data: {
    amount: number;
    invoiceNumber: string;
    customerName: string;
    remainingAmount: number;
    paidAmount: number;
  }) => Promise<CashierDeposit>;

  // العملاء
  getCustomers: () => Promise<Customer[]>;
  addCustomer: (data: {
    name: string;
    phone?: string;
    address?: string;
  }) => Promise<Customer>;
  updateCustomer: (id: number, data: Partial<Customer>) => Promise<Customer>;
  removeCustomer: (id: number) => Promise<boolean>;
  saveCustomers: (customers: Customer[]) => Promise<boolean>;

  // المدفوعات
  getPayments: () => Promise<Payment[]>;
  addPayment: (data: any) => Promise<Payment>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

// إعادة تصدير الأنواع من الملف الرئيسي
import type { Section, Expense, Employee, Withdraw, BalanceTransaction, CashierDeposit, Customer, Payment, EmployeeAddition } from '../types';
