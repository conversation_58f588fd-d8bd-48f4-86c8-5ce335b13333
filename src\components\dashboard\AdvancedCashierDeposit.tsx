import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Receipt, DollarSign, User, FileText } from "lucide-react";
import { CashierDeposit, Customer } from "@/types";

const currency = (a: number) => a.toLocaleString("ar-LY") + " د.ل";

interface AdvancedCashierDepositProps {
  cashierDeposits: CashierDeposit[];
  customers: Customer[];
  onAdvancedDeposit: (depositData: Omit<CashierDeposit, 'id' | 'date' | 'status'>) => void;
}

export default function AdvancedCashierDeposit({
  cashierDeposits,
  customers,
  onAdvancedDeposit
}: AdvancedCashierDepositProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [depositForm, setDepositForm] = useState({
    amount: "",
    invoiceNumber: "",
    customerId: "",
    customerName: "",
    remainingAmount: "",
    paidAmount: ""
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const amount = Number(depositForm.amount);
    const remainingAmount = Number(depositForm.remainingAmount);
    const paidAmount = Number(depositForm.paidAmount);
    
    if (amount <= 0) {
      alert("يجب إدخال مبلغ صحيح");
      return;
    }
    
    if (!depositForm.invoiceNumber.trim()) {
      alert("يجب إدخال رقم الفاتورة");
      return;
    }
    
    if (!depositForm.customerId) {
      alert("يجب اختيار العميل");
      return;
    }

    const selectedCustomer = customers.find(c => c.id === Number(depositForm.customerId));
    if (!selectedCustomer) {
      alert("العميل المحدد غير موجود");
      return;
    }

    onAdvancedDeposit({
      amount,
      invoiceNumber: depositForm.invoiceNumber.trim(),
      customerId: Number(depositForm.customerId),
      customerName: selectedCustomer.name,
      remainingAmount,
      paidAmount
    });

    setDepositForm({
      amount: "",
      invoiceNumber: "",
      customerId: "",
      customerName: "",
      remainingAmount: "",
      paidAmount: ""
    });
    setIsDialogOpen(false);
  };

  const totalAmount = cashierDeposits.reduce((sum, deposit) => sum + deposit.amount, 0);
  const totalPaid = cashierDeposits.reduce((sum, deposit) => sum + deposit.paidAmount, 0);
  const totalRemaining = cashierDeposits.reduce((sum, deposit) => sum + deposit.remainingAmount, 0);

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Receipt className="h-6 w-6" />
            <span>إدارة الفواتير والإيداعات</span>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                إضافة فاتورة جديدة
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة فاتورة جديدة</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="invoiceNumber">رقم الفاتورة *</Label>
                  <Input
                    id="invoiceNumber"
                    value={depositForm.invoiceNumber}
                    onChange={(e) => setDepositForm(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                    placeholder="أدخل رقم الفاتورة"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="customerId">العميل *</Label>
                  <Select
                    value={depositForm.customerId}
                    onValueChange={(value) => setDepositForm(prev => ({ ...prev, customerId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر العميل" />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id.toString()}>
                          {customer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="amount">إجمالي الفاتورة *</Label>
                  <Input
                    id="amount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={depositForm.amount}
                    onChange={(e) => setDepositForm(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder="أدخل إجمالي مبلغ الفاتورة"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="paidAmount">المبلغ المدفوع</Label>
                  <Input
                    id="paidAmount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={depositForm.paidAmount}
                    onChange={(e) => setDepositForm(prev => ({ ...prev, paidAmount: e.target.value }))}
                    placeholder="أدخل المبلغ المدفوع"
                  />
                </div>
                
                <div>
                  <Label htmlFor="remainingAmount">المبلغ المتبقي</Label>
                  <Input
                    id="remainingAmount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={depositForm.remainingAmount}
                    onChange={(e) => setDepositForm(prev => ({ ...prev, remainingAmount: e.target.value }))}
                    placeholder="أدخل المبلغ المتبقي"
                  />
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button type="submit" className="flex-1">إضافة الفاتورة</Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsDialogOpen(false)}
                    className="flex-1"
                  >
                    إلغاء
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        {/* إحصائيات الفواتير */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">عدد الفواتير</p>
                  <p className="text-2xl font-bold">{cashierDeposits.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">إجمالي الفواتير</p>
                  <p className="text-2xl font-bold text-blue-600">{currency(totalAmount)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">إجمالي المدفوع</p>
                  <p className="text-2xl font-bold text-green-600">{currency(totalPaid)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-red-600" />
                <div>
                  <p className="text-sm text-muted-foreground">إجمالي المتبقي</p>
                  <p className="text-2xl font-bold text-red-600">{currency(totalRemaining)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* جدول الفواتير */}
        {cashierDeposits.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Receipt className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>لا توجد فواتير مسجلة حتى الآن</p>
            <p className="text-sm">ابدأ بإضافة فاتورة جديدة</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>رقم الفاتورة</TableHead>
                <TableHead>اسم العميل</TableHead>
                <TableHead>إجمالي الفاتورة</TableHead>
                <TableHead>المبلغ المدفوع</TableHead>
                <TableHead>المبلغ المتبقي</TableHead>
                <TableHead>التاريخ</TableHead>
                <TableHead>الحالة</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cashierDeposits.map((deposit) => (
                <TableRow key={deposit.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      {deposit.invoiceNumber}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      {deposit.customerName}
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {currency(deposit.amount)}
                  </TableCell>
                  <TableCell className="text-green-600 font-medium">
                    {currency(deposit.paidAmount)}
                  </TableCell>
                  <TableCell className="text-red-600 font-medium">
                    {currency(deposit.remainingAmount)}
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {new Date(deposit.date).toLocaleDateString("ar-LY")}
                  </TableCell>
                  <TableCell>
                    {deposit.remainingAmount > 0 ? (
                      <Badge variant="destructive">غير مكتملة</Badge>
                    ) : (
                      <Badge variant="default">مكتملة</Badge>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
