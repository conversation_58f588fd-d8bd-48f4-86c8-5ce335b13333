
import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { Section } from "@/types";
import { useElectronAPI } from "./useElectronAPI";

const currency = (a: number) => a.toLocaleString("ar-LY") + " د.ل";

export const useSections = () => {
  const [sections, setSections] = useState<Section[]>([]);
  const [newSectionName, setNewSectionName] = useState("");
  const [expenseInputs, setExpenseInputs] = useState<{ [id: number]: { desc: string; amount: string; date: Date | null } }>({});
  const { isElectron, api } = useElectronAPI();

  // تحميل الأقسام عند بدء التطبيق
  useEffect(() => {
    const loadSections = async () => {
      if (isElectron && api) {
        try {
          const loadedSections = await api.getSections();
          setSections(loadedSections);
        } catch (error) {
          console.error('Error loading sections:', error);
        }
      } else {
        // استخدام localStorage كبديل للمتصفح
        const savedSections = localStorage.getItem('sections');
        if (savedSections) {
          setSections(JSON.parse(savedSections));
        }
      }
    };

    loadSections();
  }, [isElectron, api]);

  // حفظ الأقسام
  const saveSections = (newSections: Section[]) => {
    if (!isElectron) {
      localStorage.setItem('sections', JSON.stringify(newSections));
    }
  };

  const handleAddSection = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newSectionName.trim()) {
      toast({ title: "تحذير", description: "يرجى إدخال اسم القسم.", variant: "destructive" });
      return;
    }

    if (isElectron && api) {
      try {
        const newSection = await api.addSection(newSectionName.trim());
        setSections([...sections, newSection]);
      } catch (error) {
        console.error('Error adding section:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء إضافة القسم.", variant: "destructive" });
        return;
      }
    } else {
      const newSection = {
        id: sections.length === 0 ? 1 : Math.max(...sections.map((s) => s.id)) + 1,
        name: newSectionName.trim(),
        expenses: [],
      };
      const newSections = [...sections, newSection];
      setSections(newSections);
      saveSections(newSections);
    }

    toast({ title: "تم إضافة القسم", description: `تمت إضافة قسم "${newSectionName}" بنجاح.` });
    setNewSectionName("");
  };

  const handleDeleteSection = async (id: number) => {
    if (isElectron && api) {
      try {
        await api.deleteSection(id);
        setSections(sections.filter((sec) => sec.id !== id));
      } catch (error) {
        console.error('Error deleting section:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء حذف القسم.", variant: "destructive" });
        return;
      }
    } else {
      const newSections = sections.filter((sec) => sec.id !== id);
      setSections(newSections);
      saveSections(newSections);
    }

    toast({ title: "تم الحذف", description: `تم حذف القسم بنجاح.` });
  };

  const handleExpenseInputChange = (sectionId: number, field: "desc" | "amount" | "date", value: string | Date | null) => {
    setExpenseInputs((inputs) => ({
      ...inputs,
      [sectionId]: { ...(inputs[sectionId] || { desc: "", amount: "", date: null }), [field]: value },
    }));
  };

  const handleAddExpense = async (sectionId: number, e: React.FormEvent, cashierBalance: number, deductFromCashier: (amount: number) => void) => {
    e.preventDefault();
    const inputs = expenseInputs[sectionId] || { desc: "", amount: "", date: null };
    const val = Number(inputs.amount);
    if (!inputs.desc.trim() || val <= 0 || !inputs.date) {
      toast({ title: "تحذير", description: "يرجى إدخال وصف ومبلغ وتاريخ المصروف.", variant: "destructive" });
      return;
    }
    if (val > cashierBalance) {
      toast({ title: "تحذير", description: "الرصيد غير كافٍ في الخزينة.", variant: "destructive" });
      return;
    }

    if (isElectron && api) {
      try {
        const newExpense = await api.addExpense({
          sectionId,
          description: inputs.desc.trim(),
          amount: val,
          date: (inputs.date as Date).toISOString(),
        });

        setSections(sections.map((sec) =>
          sec.id === sectionId
            ? { ...sec, expenses: [...sec.expenses, newExpense] }
            : sec
        ));
      } catch (error) {
        console.error('Error adding expense:', error);
        toast({ title: "خطأ", description: "حدث خطأ أثناء إضافة المصروف.", variant: "destructive" });
        return;
      }
    } else {
      const newExpense = {
        id: sections.find(s => s.id === sectionId)?.expenses.length === 0 ? 1 :
            Math.max(...(sections.find(s => s.id === sectionId)?.expenses.map((ex) => ex.id) || [0])) + 1,
        desc: inputs.desc.trim(),
        amount: val,
        date: (inputs.date as Date).toISOString(),
      };

      const newSections = sections.map((sec) =>
        sec.id === sectionId
          ? { ...sec, expenses: [...sec.expenses, newExpense] }
          : sec
      );
      setSections(newSections);
      saveSections(newSections);
    }

    deductFromCashier(val);
    toast({ title: "تمت إضافة مصروف", description: `تمت إضافة مصروف "${inputs.desc}" بقيمة ${currency(val)}` });
    setExpenseInputs((inputs) => ({ ...inputs, [sectionId]: { desc: "", amount: "", date: null } }));
  };

  return {
    sections,
    newSectionName,
    setNewSectionName,
    handleAddSection,
    handleDeleteSection,
    expenseInputs,
    handleExpenseInputChange,
    handleAddExpense,
  };
};
