
import * as React from "react";
import Header from "@/components/dashboard/Header";
import SectionsManager from "@/components/dashboard/SectionsManager";

import { useAppContext } from "@/hooks/useAppContext";

const Index = () => {
  const {
    sections,
    employees,
    cashierBalance,
    deposit,
    setDeposit,
    handleDeposit,
    newSectionName,
    setNewSectionName,
    handleAddSection,
    handleDeleteSection,
    expenseInputs,
    handleExpenseInputChange,
    handleAddExpense,
    currency,
  } = useAppContext();

  return (
    <>
      <Header
        cashierBalance={cashierBalance}
        deposit={deposit}
        onDepositChange={setDeposit}
        onDepositSubmit={handleDeposit}
        sectionsCount={sections.length}
        currency={currency}
      />
      <main>
        <SectionsManager
          sections={sections}
          cashierBalance={cashierBalance}
          newSectionName={newSectionName}
          onNewSectionNameChange={setNewSectionName}
          onAddSection={handleAddSection}
          onDeleteSection={handleDeleteSection}
          expenseInputs={expenseInputs}
          onExpenseInputChange={handleExpenseInputChange}
          onAddExpense={handleAddExpense}
          currency={currency}
        />
      </main>
    </>
  );
};

export default Index;
