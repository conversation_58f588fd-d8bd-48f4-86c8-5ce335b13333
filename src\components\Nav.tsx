import { NavLink } from "react-router-dom";
import { Factory } from "lucide-react";
const Nav = () => {
  const linkClasses = "px-4 py-2 rounded-md text-sm font-medium transition-colors";
  const activeLinkClasses = "bg-primary text-primary-foreground";
  const inactiveLinkClasses = "text-muted-foreground hover:bg-muted";
  return <header className="p-4 border-b bg-card shadow-sm sticky top-0 bg-opacity-80 backdrop-blur-md z-10">
      <nav className="flex items-center justify-between container mx-auto">
        <NavLink to="/" className="flex items-center gap-3">
          <Factory className="h-8 w-8 text-primary" />
          <h1 className="text-xl font-bold text-primary tracking-wide">اتش قروب - المنظومة المالية</h1>
        </NavLink>
        <div className="flex items-center gap-2">
          <NavLink to="/" className={({
          isActive
        }) => `${linkClasses} ${isActive ? activeLinkClasses : inactiveLinkClasses}`}>
            لوحة التحكم
          </NavLink>
          <NavLink to="/employees" className={({
          isActive
        }) => `${linkClasses} ${isActive ? activeLinkClasses : inactiveLinkClasses}`}>
            الموظفين
          </NavLink>
          <NavLink to="/customers" className={({
          isActive
        }) => `${linkClasses} ${isActive ? activeLinkClasses : inactiveLinkClasses}`}>
            العملاء
          </NavLink>
          <NavLink to="/account-statement" className={({
          isActive
        }) => `${linkClasses} ${isActive ? activeLinkClasses : inactiveLinkClasses}`}>
            كشف الحساب
          </NavLink>
        </div>
      </nav>
    </header>;
};
export default Nav;