# نظام إدارة مصنع الأثاث المالي

## نظرة عامة

هذا تطبيق سطح مكتب شامل لإدارة النظام المالي لمصنع الأثاث، مبني باستخدام تقنيات حديثة ومصمم خصيصاً للبيئة العربية.

## الميزات الرئيسية

### 🏦 إدارة الخزينة المركزية المطورة
- تتبع رصيد الخزينة الرئيسية
- **إيداع متقدم** مع حقول تفصيلية:
  - رقم الفاتورة
  - اسم العميل
  - المبلغ المتبقي (الباقي)
  - المبلغ المدفوع
- عرض الرصيد الحالي في الوقت الفعلي
- تتبع شامل لجميع الفواتير والمدفوعات

### 🏭 إدارة الأقسام
- إنشاء وحذف أقسام المصنع
- تسجيل مصروفات كل قسم
- تتبع تفصيلي للنفقات مع التاريخ والوصف
- التحقق من كفاية الرصيد قبل الصرف

### 👥 إدارة الموظفين المحسنة
- إضافة وحذف الموظفين مع حقل **المرتب الثابت**
- تحديد رصيد ابتدائي لكل موظف
- تحديد حد أقصى للسحب
- تسجيل سحوبات الموظفين
- تتبع معاملات الرصيد (إيداع/سحب)
- عرض **إجمالي الإيداعات** و **إجمالي السحوبات** لكل موظف
- **نظام خصم تلقائي**: خصم المبالغ الزائدة من المرتب عند تجاوز الحد الأقصى
- عرض تقارير شهرية مفصلة للسحوبات

### 🧑‍💼 إدارة العملاء (جديد)
- إضافة وتعديل وحذف العملاء
- تسجيل بيانات العميل (الاسم، الهاتف، العنوان)
- ربط العملاء بالفواتير في نظام الخزينة
- عرض إحصائيات شاملة لكل عميل:
  - عدد الفواتير
  - إجمالي المبالغ المدفوعة
  - إجمالي المبالغ المتبقية
- تقارير مفصلة لكل عميل

### 📊 نظام التقارير الشامل المحدث
- **تقرير النظرة العامة**: ملخص شامل للوضع المالي
- **تقارير الموظفين**: تفاصيل كاملة مع الإجماليات الجديدة
- **تقارير العملاء**: تحليل مفصل لكل عميل وفواتيره
- **تقارير الأقسام**: تحليل مصروفات الأقسام
- **تقارير الفواتير**: متابعة حالة الدفع والمبالغ المتبقية
- **الرسوم البيانية**: تمثيل بصري محسن للبيانات

## التقنيات المستخدمة

- **Frontend**: React 18 + TypeScript + Vite
- **Desktop Framework**: Electron
- **UI Components**: shadcn/ui + Radix UI
- **Styling**: Tailwind CSS
- **Data Persistence**: JSON Files (قابل للترقية إلى SQLite)
- **State Management**: React Context + Custom Hooks
- **Routing**: React Router (Hash Router للتوافق مع Electron)
- **Forms**: React Hook Form + Zod Validation
- **Charts**: Recharts
- **Icons**: Lucide React

## متطلبات النظام

- Windows 10/11 (64-bit)
- 4 GB RAM (الحد الأدنى)
- 500 MB مساحة فارغة على القرص الصلب

## التثبيت والتشغيل

### للمطورين

1. **استنساخ المشروع**:
```bash
git clone [repository-url]
cd furniture-factory-financial-system
```

2. **تثبيت المكتبات**:
```bash
npm install
```

3. **تشغيل في وضع التطوير**:
```bash
npm run electron:dev
```

4. **بناء التطبيق للإنتاج**:
```bash
npm run build
npm run build:electron
```

### للمستخدمين النهائيين

1. تحميل ملف التثبيت من صفحة الإصدارات
2. تشغيل ملف التثبيت واتباع التعليمات
3. تشغيل التطبيق من سطح المكتب أو قائمة البرامج

## استخدام التطبيق

### البدء السريع

1. **إيداع رصيد ابتدائي**: ابدأ بإيداع مبلغ في الخزينة المركزية
2. **إضافة أقسام**: أنشئ أقسام المصنع المختلفة
3. **إضافة موظفين**: سجل بيانات الموظفين مع أرصدتهم
4. **تسجيل المعاملات**: ابدأ في تسجيل المصروفات والسحوبات
5. **مراجعة التقارير**: استخدم نظام التقارير لمتابعة الوضع المالي

### نصائح مهمة

- ✅ تأكد من وجود رصيد كافٍ في الخزينة قبل أي عملية صرف
- ✅ راجع التقارير بانتظام لمتابعة الوضع المالي
- ✅ احتفظ بنسخ احتياطية من ملفات البيانات
- ✅ استخدم أوصاف واضحة للمعاملات لسهولة المتابعة

## حفظ البيانات

التطبيق يحفظ البيانات في ملفات JSON في المجلد التالي:
- **Windows**: `%APPDATA%/furniture-factory-financial-system/data/`
- **Development**: `./data/`

### ملفات البيانات:
- `cashier.json`: بيانات الخزينة المركزية
- `sections.json`: بيانات الأقسام والمصروفات
- `employees.json`: بيانات الموظفين والسحوبات

## الأمان والنسخ الاحتياطي

- 🔒 البيانات محفوظة محلياً على جهازك
- 💾 يُنصح بعمل نسخ احتياطية دورية لمجلد البيانات
- 🔄 يمكن استعادة البيانات بنسخ الملفات إلى المجلد الصحيح

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: [support-email]
- 🐛 تقارير الأخطاء: [GitHub Issues]
- 📖 الوثائق: [Documentation Link]

## الترخيص

هذا المشروع مرخص تحت [نوع الترخيص] - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل تقديم أي تعديلات.

---

**تم تطوير هذا التطبيق خصيصاً لمصانع الأثاث في ليبيا مع مراعاة البيئة المحلية والمتطلبات الخاصة.**
