const { db } = require('../database');

class SectionModel {
  // الحصول على جميع الأقسام مع مصروفاتها
  static getAllSections() {
    const sections = db.prepare(`
      SELECT * FROM sections ORDER BY name
    `).all();

    return sections.map(section => ({
      ...section,
      expenses: this.getSectionExpenses(section.id)
    }));
  }

  // الحصول على قسم واحد
  static getSectionById(id) {
    const section = db.prepare(`
      SELECT * FROM sections WHERE id = ?
    `).get(id);

    if (!section) return null;

    return {
      ...section,
      expenses: this.getSectionExpenses(id)
    };
  }

  // إضافة قسم جديد
  static createSection(name) {
    const stmt = db.prepare(`
      INSERT INTO sections (name)
      VALUES (?)
    `);
    
    const result = stmt.run(name);
    return this.getSectionById(result.lastInsertRowid);
  }

  // تحديث قسم
  static updateSection(id, name) {
    const stmt = db.prepare(`
      UPDATE sections 
      SET name = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    
    stmt.run(name, id);
    return this.getSectionById(id);
  }

  // حذف قسم
  static deleteSection(id) {
    const stmt = db.prepare(`DELETE FROM sections WHERE id = ?`);
    return stmt.run(id);
  }

  // الحصول على مصروفات القسم
  static getSectionExpenses(sectionId) {
    return db.prepare(`
      SELECT * FROM section_expenses 
      WHERE section_id = ? 
      ORDER BY date DESC
    `).all(sectionId).map(expense => ({
      id: expense.id,
      desc: expense.description,
      amount: expense.amount,
      date: expense.date
    }));
  }

  // إضافة مصروف للقسم
  static addSectionExpense(sectionId, description, amount, date) {
    const stmt = db.prepare(`
      INSERT INTO section_expenses (section_id, description, amount, date)
      VALUES (?, ?, ?, ?)
    `);
    
    const result = stmt.run(sectionId, description, amount, date);
    
    return {
      id: result.lastInsertRowid,
      desc: description,
      amount,
      date
    };
  }

  // حذف مصروف
  static deleteSectionExpense(expenseId) {
    const stmt = db.prepare(`DELETE FROM section_expenses WHERE id = ?`);
    return stmt.run(expenseId);
  }

  // تحديث مصروف
  static updateSectionExpense(expenseId, description, amount, date) {
    const stmt = db.prepare(`
      UPDATE section_expenses 
      SET description = ?, amount = ?, date = ?
      WHERE id = ?
    `);
    
    stmt.run(description, amount, date, expenseId);
    
    return {
      id: expenseId,
      desc: description,
      amount,
      date
    };
  }
}

module.exports = SectionModel;
